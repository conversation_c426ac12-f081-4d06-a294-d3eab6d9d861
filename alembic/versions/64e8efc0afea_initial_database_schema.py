"""Initial database schema

Revision ID: 64e8efc0afea
Revises: 
Create Date: 2025-06-05 18:00:19.180933

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '64e8efc0afea'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('faq_items',
    sa.<PERSON>umn('id', sa.Integer(), nullable=False),
    sa.Column('question_ru', sa.Text(), nullable=False),
    sa.Column('answer_ru', sa.Text(), nullable=False),
    sa.Column('question_en', sa.Text(), nullable=True),
    sa.Column('answer_en', sa.Text(), nullable=True),
    sa.Column('category', sa.String(length=100), nullable=True),
    sa.Column('order_index', sa.Integer(), nullable=True),
    sa.Column('is_active', sa.<PERSON>(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_faq_active', 'faq_items', ['is_active'], unique=False)
    op.create_index('idx_faq_category', 'faq_items', ['category'], unique=False)
    op.create_index('idx_faq_order', 'faq_items', ['order_index'], unique=False)
    op.create_index(op.f('ix_faq_items_id'), 'faq_items', ['id'], unique=False)
    op.create_table('subscription_plans',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('price', sa.DECIMAL(precision=10, scale=2), nullable=False),
    sa.Column('duration_days', sa.Integer(), nullable=False),
    sa.Column('traffic_limit_gb', sa.Integer(), nullable=True),
    sa.Column('max_devices', sa.Integer(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('sort_order', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_plan_active', 'subscription_plans', ['is_active'], unique=False)
    op.create_index('idx_plan_sort', 'subscription_plans', ['sort_order'], unique=False)
    op.create_index(op.f('ix_subscription_plans_id'), 'subscription_plans', ['id'], unique=False)
    op.create_table('system_logs',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('level', sa.String(length=20), nullable=False),
    sa.Column('message', sa.Text(), nullable=False),
    sa.Column('module', sa.String(length=100), nullable=True),
    sa.Column('user_id', sa.BigInteger(), nullable=True),
    sa.Column('log_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_log_created', 'system_logs', ['created_at'], unique=False)
    op.create_index('idx_log_level', 'system_logs', ['level'], unique=False)
    op.create_index('idx_log_module', 'system_logs', ['module'], unique=False)
    op.create_index('idx_log_user_id', 'system_logs', ['user_id'], unique=False)
    op.create_index(op.f('ix_system_logs_id'), 'system_logs', ['id'], unique=False)
    op.create_table('users',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('telegram_id', sa.BigInteger(), nullable=False),
    sa.Column('username', sa.String(length=255), nullable=True),
    sa.Column('first_name', sa.String(length=255), nullable=True),
    sa.Column('last_name', sa.String(length=255), nullable=True),
    sa.Column('language_code', sa.String(length=10), nullable=True),
    sa.Column('balance', sa.DECIMAL(precision=10, scale=2), nullable=True),
    sa.Column('referral_code', sa.String(length=50), nullable=True),
    sa.Column('referred_by', sa.BigInteger(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_admin', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['referred_by'], ['users.telegram_id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_user_active', 'users', ['is_active'], unique=False)
    op.create_index('idx_user_referral_code', 'users', ['referral_code'], unique=False)
    op.create_index('idx_user_telegram_id', 'users', ['telegram_id'], unique=False)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_referral_code'), 'users', ['referral_code'], unique=True)
    op.create_index(op.f('ix_users_telegram_id'), 'users', ['telegram_id'], unique=True)
    op.create_table('promo_codes',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('code', sa.String(length=100), nullable=False),
    sa.Column('discount_type', sa.String(length=20), nullable=False),
    sa.Column('discount_value', sa.DECIMAL(precision=10, scale=2), nullable=False),
    sa.Column('max_uses', sa.Integer(), nullable=True),
    sa.Column('current_uses', sa.Integer(), nullable=True),
    sa.Column('valid_from', sa.DateTime(), nullable=True),
    sa.Column('valid_until', sa.DateTime(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_by', sa.BigInteger(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['users.telegram_id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_promo_active', 'promo_codes', ['is_active'], unique=False)
    op.create_index('idx_promo_code', 'promo_codes', ['code'], unique=False)
    op.create_index('idx_promo_valid_until', 'promo_codes', ['valid_until'], unique=False)
    op.create_index(op.f('ix_promo_codes_code'), 'promo_codes', ['code'], unique=True)
    op.create_index(op.f('ix_promo_codes_id'), 'promo_codes', ['id'], unique=False)
    op.create_table('support_tickets',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('user_id', sa.BigInteger(), nullable=False),
    sa.Column('subject', sa.String(length=255), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('priority', sa.String(length=20), nullable=True),
    sa.Column('assigned_to', sa.BigInteger(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('closed_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['assigned_to'], ['users.telegram_id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.telegram_id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_ticket_created', 'support_tickets', ['created_at'], unique=False)
    op.create_index('idx_ticket_priority', 'support_tickets', ['priority'], unique=False)
    op.create_index('idx_ticket_status', 'support_tickets', ['status'], unique=False)
    op.create_index('idx_ticket_user_id', 'support_tickets', ['user_id'], unique=False)
    op.create_index(op.f('ix_support_tickets_id'), 'support_tickets', ['id'], unique=False)
    op.create_table('user_subscriptions',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('user_id', sa.BigInteger(), nullable=False),
    sa.Column('plan_id', sa.Integer(), nullable=False),
    sa.Column('marzban_user_id', sa.String(length=255), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('start_date', sa.DateTime(), nullable=True),
    sa.Column('end_date', sa.DateTime(), nullable=True),
    sa.Column('traffic_used_gb', sa.DECIMAL(precision=10, scale=2), nullable=True),
    sa.Column('config_url', sa.Text(), nullable=True),
    sa.Column('subscription_url', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['plan_id'], ['subscription_plans.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.telegram_id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_subscription_end_date', 'user_subscriptions', ['end_date'], unique=False)
    op.create_index('idx_subscription_marzban_id', 'user_subscriptions', ['marzban_user_id'], unique=False)
    op.create_index('idx_subscription_status', 'user_subscriptions', ['status'], unique=False)
    op.create_index('idx_subscription_user_id', 'user_subscriptions', ['user_id'], unique=False)
    op.create_index(op.f('ix_user_subscriptions_id'), 'user_subscriptions', ['id'], unique=False)
    op.create_index(op.f('ix_user_subscriptions_marzban_user_id'), 'user_subscriptions', ['marzban_user_id'], unique=False)
    op.create_table('ticket_messages',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('ticket_id', sa.BigInteger(), nullable=False),
    sa.Column('sender_id', sa.BigInteger(), nullable=False),
    sa.Column('message_text', sa.Text(), nullable=False),
    sa.Column('is_from_admin', sa.Boolean(), nullable=True),
    sa.Column('attachments', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['sender_id'], ['users.telegram_id'], ),
    sa.ForeignKeyConstraint(['ticket_id'], ['support_tickets.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_message_created', 'ticket_messages', ['created_at'], unique=False)
    op.create_index('idx_message_sender_id', 'ticket_messages', ['sender_id'], unique=False)
    op.create_index('idx_message_ticket_id', 'ticket_messages', ['ticket_id'], unique=False)
    op.create_index(op.f('ix_ticket_messages_id'), 'ticket_messages', ['id'], unique=False)
    op.create_table('transactions',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('user_id', sa.BigInteger(), nullable=False),
    sa.Column('amount', sa.DECIMAL(precision=10, scale=2), nullable=False),
    sa.Column('currency', sa.String(length=10), nullable=True),
    sa.Column('payment_system', sa.String(length=50), nullable=True),
    sa.Column('payment_id', sa.String(length=255), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('subscription_id', sa.BigInteger(), nullable=True),
    sa.Column('promo_code_id', sa.Integer(), nullable=True),
    sa.Column('payment_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['promo_code_id'], ['promo_codes.id'], ),
    sa.ForeignKeyConstraint(['subscription_id'], ['user_subscriptions.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.telegram_id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_transaction_created', 'transactions', ['created_at'], unique=False)
    op.create_index('idx_transaction_payment_id', 'transactions', ['payment_id'], unique=False)
    op.create_index('idx_transaction_status', 'transactions', ['status'], unique=False)
    op.create_index('idx_transaction_user_id', 'transactions', ['user_id'], unique=False)
    op.create_index(op.f('ix_transactions_id'), 'transactions', ['id'], unique=False)
    op.create_index(op.f('ix_transactions_payment_id'), 'transactions', ['payment_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_transactions_payment_id'), table_name='transactions')
    op.drop_index(op.f('ix_transactions_id'), table_name='transactions')
    op.drop_index('idx_transaction_user_id', table_name='transactions')
    op.drop_index('idx_transaction_status', table_name='transactions')
    op.drop_index('idx_transaction_payment_id', table_name='transactions')
    op.drop_index('idx_transaction_created', table_name='transactions')
    op.drop_table('transactions')
    op.drop_index(op.f('ix_ticket_messages_id'), table_name='ticket_messages')
    op.drop_index('idx_message_ticket_id', table_name='ticket_messages')
    op.drop_index('idx_message_sender_id', table_name='ticket_messages')
    op.drop_index('idx_message_created', table_name='ticket_messages')
    op.drop_table('ticket_messages')
    op.drop_index(op.f('ix_user_subscriptions_marzban_user_id'), table_name='user_subscriptions')
    op.drop_index(op.f('ix_user_subscriptions_id'), table_name='user_subscriptions')
    op.drop_index('idx_subscription_user_id', table_name='user_subscriptions')
    op.drop_index('idx_subscription_status', table_name='user_subscriptions')
    op.drop_index('idx_subscription_marzban_id', table_name='user_subscriptions')
    op.drop_index('idx_subscription_end_date', table_name='user_subscriptions')
    op.drop_table('user_subscriptions')
    op.drop_index(op.f('ix_support_tickets_id'), table_name='support_tickets')
    op.drop_index('idx_ticket_user_id', table_name='support_tickets')
    op.drop_index('idx_ticket_status', table_name='support_tickets')
    op.drop_index('idx_ticket_priority', table_name='support_tickets')
    op.drop_index('idx_ticket_created', table_name='support_tickets')
    op.drop_table('support_tickets')
    op.drop_index(op.f('ix_promo_codes_id'), table_name='promo_codes')
    op.drop_index(op.f('ix_promo_codes_code'), table_name='promo_codes')
    op.drop_index('idx_promo_valid_until', table_name='promo_codes')
    op.drop_index('idx_promo_code', table_name='promo_codes')
    op.drop_index('idx_promo_active', table_name='promo_codes')
    op.drop_table('promo_codes')
    op.drop_index(op.f('ix_users_telegram_id'), table_name='users')
    op.drop_index(op.f('ix_users_referral_code'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index('idx_user_telegram_id', table_name='users')
    op.drop_index('idx_user_referral_code', table_name='users')
    op.drop_index('idx_user_active', table_name='users')
    op.drop_table('users')
    op.drop_index(op.f('ix_system_logs_id'), table_name='system_logs')
    op.drop_index('idx_log_user_id', table_name='system_logs')
    op.drop_index('idx_log_module', table_name='system_logs')
    op.drop_index('idx_log_level', table_name='system_logs')
    op.drop_index('idx_log_created', table_name='system_logs')
    op.drop_table('system_logs')
    op.drop_index(op.f('ix_subscription_plans_id'), table_name='subscription_plans')
    op.drop_index('idx_plan_sort', table_name='subscription_plans')
    op.drop_index('idx_plan_active', table_name='subscription_plans')
    op.drop_table('subscription_plans')
    op.drop_index(op.f('ix_faq_items_id'), table_name='faq_items')
    op.drop_index('idx_faq_order', table_name='faq_items')
    op.drop_index('idx_faq_category', table_name='faq_items')
    op.drop_index('idx_faq_active', table_name='faq_items')
    op.drop_table('faq_items')
    # ### end Alembic commands ###
