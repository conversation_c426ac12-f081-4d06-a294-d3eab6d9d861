["tests/test_database.py::TestSubscriptionPlanCRUD::test_get_active_plans", "tests/test_database.py::TestSubscriptionPlanCRUD::test_get_nonexistent_plan", "tests/test_database.py::TestSubscriptionPlanCRUD::test_get_plan_by_id", "tests/test_database.py::TestUserCRUD::test_create_new_user", "tests/test_database.py::TestUserCRUD::test_get_existing_user", "tests/test_database.py::TestUserCRUD::test_get_nonexistent_user", "tests/test_database.py::TestUserCRUD::test_get_user_by_telegram_id", "tests/test_database.py::TestUserSubscriptionCRUD::test_create_subscription", "tests/test_database.py::TestUserSubscriptionCRUD::test_get_empty_subscriptions", "tests/test_database.py::TestUserSubscriptionCRUD::test_get_user_subscriptions", "tests/test_utils.py::TestUtilityFunctions::test_create_marzban_username", "tests/test_utils.py::TestUtilityFunctions::test_create_marzban_username_length_limit", "tests/test_utils.py::TestUtilityFunctions::test_create_marzban_username_with_suffix", "tests/test_utils.py::TestUtilityFunctions::test_format_bytes", "tests/test_utils.py::TestUtilityFunctions::test_format_bytes_large_values", "tests/test_utils.py::TestUtilityFunctions::test_generate_random_string", "tests/test_utils.py::TestUtilityFunctions::test_generate_random_string_custom_length", "tests/test_utils.py::TestUtilityFunctions::test_generate_referral_code", "tests/test_utils.py::TestUtilityFunctions::test_generate_referral_code_custom_length", "tests/test_utils.py::TestUtilityFunctions::test_sanitize_username", "tests/test_utils.py::TestUtilityFunctions::test_sanitize_username_empty", "tests/test_utils.py::TestUtilityFunctions::test_validate_telegram_id_invalid", "tests/test_utils.py::TestUtilityFunctions::test_validate_telegram_id_valid"]