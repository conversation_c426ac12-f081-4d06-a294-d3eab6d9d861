<!DOCTYPE html>
<html lang="">
<head>
    <meta charset="UTF-8"/>
    <title>${site_title}</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <meta content="width=device-width, initial-scale=1, maximum-scale=1" name="viewport"/>
    <meta content="IE=Edge" http-equiv="X-UA-Compatible"/>
    <link href="${site_icon}" rel="shortcut icon" type="image/x-icon"/>
    <link href="${cdn}/${pkg}/sdk/sdk.css" rel="stylesheet" title="default"/>
    <link href="${cdn}/${pkg}/sdk/helper.css" rel="stylesheet"/>
    <link href="${cdn}/${pkg}/sdk/iconfont.css" rel="stylesheet"/>
    ${theme_css}
    <script src="${cdn}/${pkg}/sdk/sdk.js"></script>
    <script src="${cdn}/vue@2.7.14/dist/vue.min.js"></script>
    <script src="${cdn}/history@5.3.0/umd/history.production.min.js"></script>
    <style>
        html, body,
        .app-wrapper {
            position: relative;
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
        }

        /*DropDownButton组件下拉菜单样式修改*/
        .amis-scope .cxd-DropDown-menu {
            min-width: 100%;
            text-align: center;
        }
    </style>
</head>
<body>
<div class="app-wrapper" id="root"></div>
<script>
    (function () {
        let amis = amisRequire('amis/embed');
        const match = amisRequire('path-to-regexp').match;

        // 如果想用 browserHistory 请切换下这处代码, 其他不用变
        // const history = HistoryLibrary.createBrowserHistory();
        const history = HistoryLibrary.createHashHistory();
        const app = ${AmisSchemaJson};

        function normalizeLink(to, location = history.location) {
            to = to || '';

            if (to && to[0] === '#') {
                to = location.pathname + location.search + to;
            } else if (to && to[0] === '?') {
                to = location.pathname + to;
            }

            const idx = to.indexOf('?');
            const idx2 = to.indexOf('#');
            let pathname = ~idx ? to.substring(0, idx) : ~idx2 ? to.substring(0, idx2) : to;
            let search = ~idx ? to.substring(idx, ~idx2 ? idx2 : undefined) : '';
            let hash = ~idx2 ? to.substring(idx2) : location.hash;
            if (!pathname) {
                pathname = location.pathname;
            } else if (pathname[0] != '/' && !/^https?\:\/\//.test(pathname)) {
                let relativeBase = location.pathname;
                const paths = relativeBase.split('/');
                paths.pop();
                let m;
                while ((m = /^\.\.?\//.exec(pathname))) {
                    if (m[0] === '../') {
                        paths.pop();
                    }
                    pathname = pathname.substring(m[0].length);
                }
                pathname = paths.concat(pathname).join('/');
            }
            return pathname + search + hash;
        }

        function isCurrentUrl(to, ctx) {
            if (!to) {
                return false;
            }
            const pathname = history.location.pathname;
            const link = normalizeLink(to, {
                ...location,
                pathname,
                hash: ''
            });

            if (!~link.indexOf('http') && ~link.indexOf(':')) {
                let strict = ctx && ctx.strict;
                return match(link, {
                    decode: decodeURIComponent,
                    strict: typeof strict !== 'undefined' ? strict : true
                })(pathname);
            }

            return decodeURI(pathname) === link;
        }

        let amisInstance = amis.embed(
            '#root',
            app,
            {location: history.location, locale: "${locale}"},
            {
                // watchRouteChange: fn => {
                //   return history.listen(fn);
                // },
                updateLocation: (location, replace) => {
                    location = normalizeLink(location);
                    if (location === 'goBack') {
                        return history.goBack();
                    } else if (
                        (!/^https?\:\/\//.test(location) &&
                            location ===
                            history.location.pathname + history.location.search) ||
                        location === history.location.href
                    ) {
                        // 目标地址和当前地址一样，不处理，免得重复刷新
                        return;
                    } else if (/^https?\:\/\//.test(location) || !history) {
                        return (window.location.href = location);
                    }

                    history[replace ? 'replace' : 'push'](location);
                },
                jumpTo: (to, action) => {
                    if (to === 'goBack') {
                        return history.goBack();
                    }

                    to = normalizeLink(to);

                    if (isCurrentUrl(to)) {
                        return;
                    }

                    if (action && action.actionType === 'url') {
                        action.blank === false
                            ? (window.location.href = to)
                            : window.open(to, '_blank');
                        return;
                    } else if (action && action.blank) {
                        window.open(to, '_blank');
                        return;
                    }

                    if (/^https?:\/\//.test(to)) {
                        window.location.href = to;
                    } else if (
                        (!/^https?\:\/\//.test(to) &&
                            to === history.pathname + history.location.search) ||
                        to === history.location.href
                    ) {
                        // do nothing
                    } else if (location.hash && to.indexOf("?") > -1) {
                        //如果当前页面有hash，且跳转的页面有参数，将hash拼接到参数后面
                        const [hash, search] = to.split("?");
                        window.location.href = location.pathname + "?" + search + "#" + hash;
                    } else {
                        history.push(to);
                    }
                },
                isCurrentUrl: isCurrentUrl,
                theme: "${theme}"
            }
        );

        history.listen(state => {
            amisInstance.updateProps({
                location: state.location || state,
                locale: "${locale}"
            });
        });
    })();
</script>
</body>
</html>
