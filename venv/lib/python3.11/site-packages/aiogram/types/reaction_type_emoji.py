from typing import TYPE_CHECKING, Any, Literal

from ..enums import ReactionTypeType
from .reaction_type import ReactionType


class ReactionTypeEmoji(ReactionType):
    """
    The reaction is based on an emoji.

    Source: https://core.telegram.org/bots/api#reactiontypeemoji
    """

    type: Literal[ReactionTypeType.EMOJI] = ReactionTypeType.EMOJI
    """Type of the reaction, always 'emoji'"""
    emoji: str
    """Reaction emoji. Currently, it can be one of "👍", "👎", "❤", "🔥", "🥰", "👏", "😁", "🤔", "🤯", "😱", "🤬", "😢", "🎉", "🤩", "🤮", "💩", "🙏", "👌", "🕊", "🤡", "🥱", "🥴", "😍", "🐳", "❤‍🔥", "🌚", "🌭", "💯", "🤣", "⚡", "🍌", "🏆", "💔", "🤨", "😐", "🍓", "🍾", "💋", "🖕", "😈", "😴", "😭", "🤓", "👻", "👨‍💻", "👀", "🎃", "🙈", "😇", "😨", "🤝", "✍", "🤗", "🫡", "🎅", "🎄", "☃", "💅", "🤪", "🗿", "🆒", "💘", "🙉", "🦄", "😘", "💊", "🙊", "😎", "👾", "🤷‍♂", "🤷", "🤷‍♀", "😡" """

    if TYPE_CHECKING:
        # DO NOT EDIT MANUALLY!!!
        # This section was auto-generated via `butcher`

        def __init__(
            __pydantic__self__,
            *,
            type: Literal[ReactionTypeType.EMOJI] = ReactionTypeType.EMOJI,
            emoji: str,
            **__pydantic_kwargs: Any,
        ) -> None:
            # DO NOT EDIT MANUALLY!!!
            # This method was auto-generated via `butcher`
            # Is needed only for type checking and IDE support without any additional plugins

            super().__init__(type=type, emoji=emoji, **__pydantic_kwargs)
