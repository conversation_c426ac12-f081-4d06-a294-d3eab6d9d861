"""
Главный модуль FastAPI приложения
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.api.routers.webhooks import webhooks_router
from app.core.config import settings

# Создаем приложение FastAPI
app = FastAPI(
    title="Unveil VPN API",
    description="API для VPN сервиса с интеграцией платежных систем",
    version="1.0.0"
)

# Настраиваем CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # В продакшене указать конкретные домены
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Подключаем роутеры
app.include_router(webhooks_router)


@app.get("/")
async def root():
    """Корневой endpoint"""
    return {
        "message": "Unveil VPN API",
        "version": "1.0.0",
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """Проверка здоровья приложения"""
    return {
        "status": "healthy",
        "environment": settings.environment,
        "payment_providers": settings.available_payment_providers
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
