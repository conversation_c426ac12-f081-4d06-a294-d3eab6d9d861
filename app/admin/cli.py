"""
CLI команды для админ-панели

Содержит команды для создания администраторов,
управления ролями и инициализации системы.
"""

import asyncio
import getpass
from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession

from app.database.database import AsyncSessionLocal
from app.admin.models import AdminUser, AdminRole
from app.admin.auth import AdminAuth
from app.admin.crud import create_admin_user, get_admin_user_by_username


async def create_superuser(
    username: str,
    email: str,
    password: Optional[str] = None,
    full_name: Optional[str] = None
) -> AdminUser:
    """
    Создает суперпользователя

    Args:
        username: Имя пользователя
        email: Email
        password: Пароль (если не указан, будет запрошен)
        full_name: Полное имя

    Returns:
        Созданный администратор
    """

    if not password:
        import sys
        if sys.stdin.isatty():
            # Интерактивный режим
            password = getpass.getpass("Введите пароль для суперпользователя: ")
            confirm_password = getpass.getpass("Подтвердите пароль: ")

            if password != confirm_password:
                raise ValueError("Пароли не совпадают")
        else:
            # Неинтерактивный режим - используем дефолтный пароль
            password = "admin123"
            print("⚠️  Используется дефолтный пароль: admin123")
    
    # БД должна быть уже инициализирована

    async with AsyncSessionLocal() as db:
        # Проверяем, существует ли уже администратор с таким username
        existing = await get_admin_user_by_username(db, username)
        if existing:
            raise ValueError(f"Администратор с username '{username}' уже существует")
        
        # Создаем суперпользователя
        admin = await create_admin_user(
            db=db,
            username=username,
            email=email,
            password=password,
            full_name=full_name,
            role=AdminRole.SUPER_ADMIN,
            is_active=True,
            is_superuser=True,
            notes="Создан через CLI"
        )
        
        print(f"✅ Суперпользователь '{username}' успешно создан!")
        print(f"   Email: {email}")
        print(f"   ID: {admin.id}")
        
        return admin


async def create_admin_interactive():
    """Интерактивное создание администратора"""
    
    print("🔧 Создание нового администратора")
    print("=" * 40)
    
    # Запрашиваем данные
    username = input("Введите username: ").strip()
    if not username:
        print("❌ Username не может быть пустым")
        return
    
    email = input("Введите email: ").strip()
    if not email:
        print("❌ Email не может быть пустым")
        return
    
    full_name = input("Введите полное имя (необязательно): ").strip() or None
    
    # Выбираем роль
    print("\nДоступные роли:")
    roles = list(AdminRole)
    for i, role in enumerate(roles, 1):
        print(f"  {i}. {role.value}")
    
    while True:
        try:
            role_choice = int(input("Выберите роль (номер): "))
            if 1 <= role_choice <= len(roles):
                selected_role = roles[role_choice - 1]
                break
            else:
                print("❌ Неверный номер роли")
        except ValueError:
            print("❌ Введите число")
    
    # Суперпользователь?
    is_superuser = input("Сделать суперпользователем? (y/N): ").lower().startswith('y')
    
    # Пароль
    password = getpass.getpass("Введите пароль: ")
    confirm_password = getpass.getpass("Подтвердите пароль: ")
    
    if password != confirm_password:
        print("❌ Пароли не совпадают")
        return
    
    # Создаем администратора
    try:
        async with AsyncSessionLocal() as db:
            admin = await create_admin_user(
                db=db,
                username=username,
                email=email,
                password=password,
                full_name=full_name,
                role=selected_role,
                is_active=True,
                is_superuser=is_superuser,
                notes="Создан через интерактивный CLI"
            )
            
            print(f"\n✅ Администратор '{username}' успешно создан!")
            print(f"   Email: {email}")
            print(f"   Роль: {selected_role.value}")
            print(f"   Суперпользователь: {'Да' if is_superuser else 'Нет'}")
            print(f"   ID: {admin.id}")
            
    except Exception as e:
        print(f"❌ Ошибка создания администратора: {e}")


async def init_admin_system():
    """Инициализация системы администрирования"""
    
    print("🚀 Инициализация системы администрирования")
    print("=" * 50)
    
    async with AsyncSessionLocal() as db:
        # Проверяем, есть ли уже администраторы
        from sqlalchemy import select, func
        admin_count = await db.scalar(select(func.count(AdminUser.id)))
        
        if admin_count > 0:
            print(f"ℹ️  В системе уже есть {admin_count} администратор(ов)")
            create_new = input("Создать нового администратора? (y/N): ").lower().startswith('y')
            if not create_new:
                return
        else:
            print("ℹ️  В системе нет администраторов. Создаем первого суперпользователя...")
        
        await create_admin_interactive()


async def list_admins():
    """Список всех администраторов"""

    print("👥 Список администраторов")
    print("=" * 30)

    # БД должна быть уже инициализирована

    async with AsyncSessionLocal() as db:
        from app.admin.crud import get_admin_users
        
        admins = await get_admin_users(db, limit=100)
        
        if not admins:
            print("📭 Администраторы не найдены")
            return
        
        print(f"Найдено администраторов: {len(admins)}\n")
        
        for admin in admins:
            status = "🟢 Активен" if admin.is_active else "🔴 Неактивен"
            super_badge = " 👑" if admin.is_superuser else ""
            
            print(f"ID: {admin.id}")
            print(f"Username: {admin.username}{super_badge}")
            print(f"Email: {admin.email}")
            print(f"Роль: {admin.role.value}")
            print(f"Статус: {status}")
            print(f"Создан: {admin.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
            if admin.last_login:
                print(f"Последний вход: {admin.last_login.strftime('%Y-%m-%d %H:%M:%S')}")
            print("-" * 30)


def main():
    """Главная функция CLI"""
    import sys
    
    if len(sys.argv) < 2:
        print("Использование:")
        print("  python -m app.admin.cli init          - Инициализация системы")
        print("  python -m app.admin.cli create        - Создать администратора")
        print("  python -m app.admin.cli list          - Список администраторов")
        print("  python -m app.admin.cli superuser     - Создать суперпользователя")
        return
    
    command = sys.argv[1]
    
    if command == "init":
        asyncio.run(init_admin_system())
    elif command == "create":
        asyncio.run(create_admin_interactive())
    elif command == "list":
        asyncio.run(list_admins())
    elif command == "superuser":
        if len(sys.argv) < 4:
            print("Использование: python -m app.admin.cli superuser <username> <email> [full_name] [password]")
            return

        username = sys.argv[2]
        email = sys.argv[3]
        full_name = sys.argv[4] if len(sys.argv) > 4 else None
        password = sys.argv[5] if len(sys.argv) > 5 else None

        asyncio.run(create_superuser(username, email, password=password, full_name=full_name))
    else:
        print(f"❌ Неизвестная команда: {command}")


if __name__ == "__main__":
    main()
