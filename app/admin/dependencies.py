"""
Зависимости для админ-панели

Содержит FastAPI dependencies для работы с базой данных,
аутентификацией и авторизацией в админ-панели.
"""

from typing import AsyncGenerator, Optional
from fastapi import Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.database.database import get_db
from app.services.payment_service import PaymentService
from app.services.subscription_service import SubscriptionService
from app.services.notification_service import NotificationService
from .auth import get_current_admin_user
from .models import AdminUser, AdminPermission


async def get_admin_db() -> AsyncGenerator[AsyncSession, None]:
    """Dependency для получения сессии БД для админ-панели"""
    async for session in get_db():
        yield session


async def get_payment_service_admin(
    db: AsyncSession = Depends(get_admin_db)
) -> PaymentService:
    """Dependency для получения сервиса платежей в админ-панели"""
    return PaymentService(db)


async def get_subscription_service_admin(
    db: AsyncSession = Depends(get_admin_db)
) -> SubscriptionService:
    """Dependency для получения сервиса подписок в админ-панели"""
    return SubscriptionService(db)


async def get_notification_service_admin(
    db: AsyncSession = Depends(get_admin_db)
) -> NotificationService:
    """Dependency для получения сервиса уведомлений в админ-панели"""
    return NotificationService(db)


def require_admin_permission(permission: AdminPermission):
    """
    Создает dependency для проверки разрешений администратора
    
    Args:
        permission: Требуемое разрешение
        
    Returns:
        Dependency function
    """
    async def check_permission(
        admin: AdminUser = Depends(get_current_admin_user)
    ) -> AdminUser:
        if not admin.has_permission(permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Недостаточно прав доступа. Требуется разрешение: {permission.value}"
            )
        return admin
    
    return check_permission


def require_admin_permissions(*permissions: AdminPermission):
    """
    Создает dependency для проверки множественных разрешений (ИЛИ)
    
    Args:
        permissions: Список разрешений (достаточно одного)
        
    Returns:
        Dependency function
    """
    async def check_permissions(
        admin: AdminUser = Depends(get_current_admin_user)
    ) -> AdminUser:
        if not admin.has_any_permission(list(permissions)):
            permission_names = [p.value for p in permissions]
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Недостаточно прав доступа. Требуется одно из разрешений: {', '.join(permission_names)}"
            )
        return admin
    
    return check_permissions


async def get_admin_context(
    request: Request,
    admin: AdminUser = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_admin_db)
) -> dict:
    """
    Dependency для получения контекста админ-панели
    
    Returns:
        Словарь с информацией о текущем администраторе и контексте
    """
    return {
        "admin": admin,
        "request": request,
        "db": db,
        "permissions": [perm.value for perm in AdminPermission if admin.has_permission(perm)],
        "is_superuser": admin.is_superuser,
        "role": admin.role.value
    }


class AdminPagination:
    """Класс для пагинации в админ-панели"""
    
    def __init__(
        self,
        page: int = 1,
        per_page: int = 20,
        max_per_page: int = 100
    ):
        self.page = max(1, page)
        self.per_page = min(max(1, per_page), max_per_page)
        self.offset = (self.page - 1) * self.per_page
    
    @property
    def limit(self) -> int:
        return self.per_page
    
    def get_pagination_info(self, total_count: int) -> dict:
        """Возвращает информацию о пагинации"""
        total_pages = (total_count + self.per_page - 1) // self.per_page
        
        return {
            "page": self.page,
            "per_page": self.per_page,
            "total_count": total_count,
            "total_pages": total_pages,
            "has_next": self.page < total_pages,
            "has_prev": self.page > 1,
            "next_page": self.page + 1 if self.page < total_pages else None,
            "prev_page": self.page - 1 if self.page > 1 else None
        }


def get_admin_pagination(
    page: int = 1,
    per_page: int = 20
) -> AdminPagination:
    """Dependency для получения объекта пагинации"""
    return AdminPagination(page=page, per_page=per_page)


class AdminFilters:
    """Базовый класс для фильтров в админ-панели"""
    
    def __init__(
        self,
        search: Optional[str] = None,
        sort_by: Optional[str] = None,
        sort_order: str = "desc"
    ):
        self.search = search.strip() if search else None
        self.sort_by = sort_by
        self.sort_order = sort_order.lower() if sort_order.lower() in ["asc", "desc"] else "desc"
    
    def has_search(self) -> bool:
        """Проверяет, есть ли поисковый запрос"""
        return bool(self.search)
    
    def get_order_direction(self):
        """Возвращает направление сортировки для SQLAlchemy"""
        from sqlalchemy import asc, desc
        return asc if self.sort_order == "asc" else desc


def get_admin_filters(
    search: Optional[str] = None,
    sort_by: Optional[str] = None,
    sort_order: str = "desc"
) -> AdminFilters:
    """Dependency для получения объекта фильтров"""
    return AdminFilters(search=search, sort_by=sort_by, sort_order=sort_order)


async def validate_admin_access(
    admin: AdminUser = Depends(get_current_admin_user)
) -> AdminUser:
    """
    Базовая проверка доступа к админ-панели
    
    Проверяет, что администратор активен и имеет минимальные права
    """
    if not admin.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Аккаунт администратора деактивирован"
        )
    
    # Проверяем, что у администратора есть хотя бы одно разрешение на просмотр
    view_permissions = [
        AdminPermission.VIEW_USERS,
        AdminPermission.VIEW_SUBSCRIPTIONS,
        AdminPermission.VIEW_PAYMENTS,
        AdminPermission.VIEW_SYSTEM_STATS
    ]
    
    if not admin.has_any_permission(view_permissions) and not admin.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Недостаточно прав для доступа к админ-панели"
        )
    
    return admin
