"""
Общие Pydantic схемы для админ-панели
"""

from typing import Optional, Any, Dict
from pydantic import BaseModel, Field


class PaginationInfo(BaseModel):
    """Информация о пагинации"""
    page: int = Field(..., description="Текущая страница")
    per_page: int = Field(..., description="Элементов на странице")
    total_count: int = Field(..., description="Общее количество элементов")
    total_pages: int = Field(..., description="Общее количество страниц")
    has_next: bool = Field(..., description="Есть ли следующая страница")
    has_prev: bool = Field(..., description="Есть ли предыдущая страница")
    next_page: Optional[int] = Field(None, description="Номер следующей страницы")
    prev_page: Optional[int] = Field(None, description="Номер предыдущей страницы")


class BaseResponse(BaseModel):
    """Базовый ответ API"""
    success: bool = Field(True, description="Успешность операции")
    message: Optional[str] = Field(None, description="Сообщение")
    data: Optional[Any] = Field(None, description="Данные ответа")


class ErrorResponse(BaseModel):
    """Ответ с ошибкой"""
    success: bool = Field(False, description="Успешность операции")
    error: str = Field(..., description="Описание ошибки")
    error_code: Optional[str] = Field(None, description="Код ошибки")
    details: Optional[Dict[str, Any]] = Field(None, description="Дополнительные детали ошибки")


class SuccessResponse(BaseResponse):
    """Ответ об успешной операции"""
    success: bool = Field(True, description="Успешность операции")
    message: str = Field(..., description="Сообщение об успехе")


class FilterParams(BaseModel):
    """Параметры фильтрации"""
    search: Optional[str] = Field(None, description="Поисковый запрос")
    sort_by: Optional[str] = Field(None, description="Поле для сортировки")
    sort_order: str = Field("desc", description="Направление сортировки (asc/desc)")
    
    class Config:
        schema_extra = {
            "example": {
                "search": "john",
                "sort_by": "created_at",
                "sort_order": "desc"
            }
        }


class PaginationParams(BaseModel):
    """Параметры пагинации"""
    page: int = Field(1, ge=1, description="Номер страницы")
    per_page: int = Field(20, ge=1, le=100, description="Количество элементов на странице")
    
    class Config:
        schema_extra = {
            "example": {
                "page": 1,
                "per_page": 20
            }
        }
