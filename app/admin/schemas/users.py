"""
Pydantic схемы для управления пользователями в админ-панели
"""

from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field, EmailStr, validator

from .common import PaginationInfo


class UserBase(BaseModel):
    """Базовая схема пользователя"""
    telegram_id: int = Field(..., description="Telegram ID пользователя")
    username: str = Field(..., min_length=3, max_length=50, description="Имя пользователя")
    email: Optional[EmailStr] = Field(None, description="Email пользователя")
    full_name: Optional[str] = Field(None, max_length=255, description="Полное имя")
    language_code: str = Field("ru", description="Код языка")
    is_active: bool = Field(True, description="Активен ли пользователь")


class UserCreate(UserBase):
    """Схема для создания пользователя"""
    
    @validator('username')
    def validate_username(cls, v):
        if not v.replace('_', '').isalnum():
            raise ValueError('Username может содержать только буквы, цифры и подчеркивания')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "telegram_id": 123456789,
                "username": "john_doe",
                "email": "<EMAIL>",
                "full_name": "John Doe",
                "language_code": "ru",
                "is_active": True
            }
        }


class UserUpdate(BaseModel):
    """Схема для обновления пользователя"""
    username: Optional[str] = Field(None, min_length=3, max_length=50)
    email: Optional[EmailStr] = Field(None)
    full_name: Optional[str] = Field(None, max_length=255)
    language_code: Optional[str] = Field(None)
    is_active: Optional[bool] = Field(None)
    
    @validator('username')
    def validate_username(cls, v):
        if v and not v.replace('_', '').isalnum():
            raise ValueError('Username может содержать только буквы, цифры и подчеркивания')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "username": "john_doe_updated",
                "email": "<EMAIL>",
                "full_name": "John Doe Updated",
                "is_active": False
            }
        }


class UserResponse(BaseModel):
    """Схема ответа с информацией о пользователе"""
    id: int = Field(..., description="ID пользователя")
    telegram_id: int = Field(..., description="Telegram ID")
    username: str = Field(..., description="Имя пользователя")
    email: Optional[str] = Field(None, description="Email")
    full_name: Optional[str] = Field(None, description="Полное имя")
    language_code: str = Field(..., description="Код языка")
    is_active: bool = Field(..., description="Активен ли пользователь")
    created_at: datetime = Field(..., description="Дата создания")
    updated_at: datetime = Field(..., description="Дата обновления")
    last_login: Optional[datetime] = Field(None, description="Последний вход")
    
    # Статистика
    subscriptions_count: int = Field(0, description="Количество подписок")
    active_subscriptions_count: int = Field(0, description="Количество активных подписок")
    total_spent: float = Field(0.0, description="Общая сумма потраченных средств")
    
    class Config:
        from_attributes = True
        
        @classmethod
        def from_orm(cls, obj):
            """Создает схему из ORM объекта с дополнительными вычислениями"""
            data = {
                'id': obj.id,
                'telegram_id': obj.telegram_id,
                'username': obj.username,
                'email': obj.email,
                'full_name': obj.full_name,
                'language_code': obj.language_code,
                'is_active': obj.is_active,
                'created_at': obj.created_at,
                'updated_at': obj.updated_at,
                'last_login': obj.last_login,
            }
            
            # Вычисляем статистику подписок
            if hasattr(obj, 'subscriptions') and obj.subscriptions:
                data['subscriptions_count'] = len(obj.subscriptions)
                data['active_subscriptions_count'] = sum(
                    1 for sub in obj.subscriptions 
                    if sub.status.value == 'active'
                )
            
            # Вычисляем общую сумму трат
            if hasattr(obj, 'transactions') and obj.transactions:
                data['total_spent'] = sum(
                    float(trans.amount) for trans in obj.transactions 
                    if trans.status.value == 'completed'
                )
            
            return cls(**data)


class UserDetailResponse(UserResponse):
    """Детальная информация о пользователе"""
    registration_ip: Optional[str] = Field(None, description="IP регистрации")
    last_activity: Optional[datetime] = Field(None, description="Последняя активность")
    notes: Optional[str] = Field(None, description="Заметки администратора")
    
    # Связанные данные
    subscriptions: List[dict] = Field(default_factory=list, description="Подписки пользователя")
    recent_transactions: List[dict] = Field(default_factory=list, description="Последние транзакции")
    
    class Config:
        from_attributes = True
        
        @classmethod
        def from_orm(cls, obj):
            """Создает детальную схему из ORM объекта"""
            # Получаем базовые данные
            base_data = super().from_orm(obj).__dict__
            
            # Добавляем детальную информацию
            detail_data = {
                'registration_ip': getattr(obj, 'registration_ip', None),
                'last_activity': getattr(obj, 'last_activity', None),
                'notes': getattr(obj, 'notes', None),
            }
            
            # Добавляем подписки
            if hasattr(obj, 'subscriptions') and obj.subscriptions:
                detail_data['subscriptions'] = [
                    {
                        'id': sub.id,
                        'plan_name': sub.plan.name if sub.plan else 'Unknown',
                        'status': sub.status.value,
                        'start_date': sub.start_date.isoformat() if sub.start_date else None,
                        'end_date': sub.end_date.isoformat() if sub.end_date else None,
                        'traffic_used': sub.traffic_used,
                        'traffic_limit': sub.traffic_limit
                    }
                    for sub in obj.subscriptions
                ]
            
            # Добавляем последние транзакции
            if hasattr(obj, 'transactions') and obj.transactions:
                recent_transactions = sorted(
                    obj.transactions, 
                    key=lambda x: x.created_at, 
                    reverse=True
                )[:10]  # Последние 10 транзакций
                
                detail_data['recent_transactions'] = [
                    {
                        'id': trans.id,
                        'amount': float(trans.amount),
                        'currency': trans.currency,
                        'status': trans.status.value,
                        'payment_system': trans.payment_system,
                        'created_at': trans.created_at.isoformat()
                    }
                    for trans in recent_transactions
                ]
            
            return cls(**{**base_data, **detail_data})


class UserListResponse(BaseModel):
    """Ответ со списком пользователей"""
    users: List[UserResponse] = Field(..., description="Список пользователей")
    pagination: PaginationInfo = Field(..., description="Информация о пагинации")
    
    class Config:
        schema_extra = {
            "example": {
                "users": [
                    {
                        "id": 1,
                        "telegram_id": 123456789,
                        "username": "john_doe",
                        "email": "<EMAIL>",
                        "full_name": "John Doe",
                        "language_code": "ru",
                        "is_active": True,
                        "created_at": "2024-01-01T00:00:00Z",
                        "updated_at": "2024-01-01T00:00:00Z",
                        "last_login": "2024-01-01T12:00:00Z",
                        "subscriptions_count": 2,
                        "active_subscriptions_count": 1,
                        "total_spent": 1500.0
                    }
                ],
                "pagination": {
                    "page": 1,
                    "per_page": 20,
                    "total_count": 100,
                    "total_pages": 5,
                    "has_next": True,
                    "has_prev": False,
                    "next_page": 2,
                    "prev_page": None
                }
            }
        }


class UserStatsResponse(BaseModel):
    """Статистика по пользователям"""
    total_users: int = Field(..., description="Общее количество пользователей")
    active_users: int = Field(..., description="Количество активных пользователей")
    inactive_users: int = Field(..., description="Количество неактивных пользователей")
    users_with_active_subscriptions: int = Field(..., description="Пользователи с активными подписками")
    new_users_last_30_days: int = Field(..., description="Новые пользователи за последние 30 дней")
    
    class Config:
        schema_extra = {
            "example": {
                "total_users": 1500,
                "active_users": 1200,
                "inactive_users": 300,
                "users_with_active_subscriptions": 800,
                "new_users_last_30_days": 150
            }
        }
