"""
Pydantic схемы для аутентификации в админ-панели
"""

from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, Field, EmailStr, validator

from app.admin.models import AdminRole


class AdminLoginRequest(BaseModel):
    """Запрос на вход в админ-панель"""
    username: str = Field(..., min_length=3, max_length=50, description="Имя пользователя или email")
    password: str = Field(..., min_length=6, description="Пароль")
    
    class Config:
        schema_extra = {
            "example": {
                "username": "admin",
                "password": "secure_password"
            }
        }


class AdminTokenResponse(BaseModel):
    """Ответ с токенами аутентификации"""
    access_token: str = Field(..., description="JWT токен доступа")
    refresh_token: str = Field(..., description="JWT токен обновления")
    token_type: str = Field("bearer", description="Тип токена")
    expires_in: int = Field(..., description="Время жизни токена в секундах")
    
    class Config:
        schema_extra = {
            "example": {
                "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                "token_type": "bearer",
                "expires_in": 28800
            }
        }


class AdminUserResponse(BaseModel):
    """Информация об администраторе"""
    id: int = Field(..., description="ID администратора")
    username: str = Field(..., description="Имя пользователя")
    email: str = Field(..., description="Email")
    full_name: Optional[str] = Field(None, description="Полное имя")
    role: str = Field(..., description="Роль администратора")
    is_active: bool = Field(..., description="Активен ли администратор")
    is_superuser: bool = Field(..., description="Является ли суперпользователем")
    created_at: datetime = Field(..., description="Дата создания")
    last_login: Optional[datetime] = Field(None, description="Последний вход")
    permissions: List[str] = Field(default_factory=list, description="Список разрешений")
    
    class Config:
        from_attributes = True
        
        @classmethod
        def from_orm(cls, obj):
            """Создает схему из ORM объекта с вычислением разрешений"""
            from app.admin.models import get_role_permissions
            
            permissions = []
            if obj.is_superuser:
                from app.admin.models import AdminPermission
                permissions = [perm.value for perm in AdminPermission]
            else:
                role_permissions = get_role_permissions(obj.role)
                permissions = [perm.value for perm in role_permissions]
            
            return cls(
                id=obj.id,
                username=obj.username,
                email=obj.email,
                full_name=obj.full_name,
                role=obj.role.value,
                is_active=obj.is_active,
                is_superuser=obj.is_superuser,
                created_at=obj.created_at,
                last_login=obj.last_login,
                permissions=permissions
            )


class AdminLoginResponse(BaseModel):
    """Ответ на успешный вход"""
    user: AdminUserResponse = Field(..., description="Информация об администраторе")
    tokens: AdminTokenResponse = Field(..., description="Токены аутентификации")
    
    class Config:
        schema_extra = {
            "example": {
                "user": {
                    "id": 1,
                    "username": "admin",
                    "email": "<EMAIL>",
                    "full_name": "System Administrator",
                    "role": "super_admin",
                    "is_active": True,
                    "is_superuser": True,
                    "created_at": "2024-01-01T00:00:00Z",
                    "last_login": "2024-01-01T12:00:00Z",
                    "permissions": ["view_users", "create_users", "edit_users"]
                },
                "tokens": {
                    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                    "token_type": "bearer",
                    "expires_in": 28800
                }
            }
        }


class AdminUserCreate(BaseModel):
    """Схема для создания администратора"""
    username: str = Field(..., min_length=3, max_length=50, description="Имя пользователя")
    email: EmailStr = Field(..., description="Email")
    password: str = Field(..., min_length=8, description="Пароль")
    full_name: Optional[str] = Field(None, max_length=255, description="Полное имя")
    role: AdminRole = Field(AdminRole.VIEWER, description="Роль администратора")
    is_active: bool = Field(True, description="Активен ли администратор")
    notes: Optional[str] = Field(None, description="Заметки")
    
    @validator('username')
    def validate_username(cls, v):
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('Username может содержать только буквы, цифры, подчеркивания и дефисы')
        return v
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Пароль должен содержать минимум 8 символов')
        
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        
        if not (has_upper and has_lower and has_digit):
            raise ValueError('Пароль должен содержать заглавные и строчные буквы, а также цифры')
        
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "username": "new_admin",
                "email": "<EMAIL>",
                "password": "SecurePass123",
                "full_name": "New Administrator",
                "role": "admin",
                "is_active": True,
                "notes": "Администратор для управления пользователями"
            }
        }


class AdminUserUpdate(BaseModel):
    """Схема для обновления администратора"""
    username: Optional[str] = Field(None, min_length=3, max_length=50)
    email: Optional[EmailStr] = Field(None)
    password: Optional[str] = Field(None, min_length=8)
    full_name: Optional[str] = Field(None, max_length=255)
    role: Optional[AdminRole] = Field(None)
    is_active: Optional[bool] = Field(None)
    notes: Optional[str] = Field(None)
    
    @validator('username')
    def validate_username(cls, v):
        if v and not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('Username может содержать только буквы, цифры, подчеркивания и дефисы')
        return v
    
    @validator('password')
    def validate_password(cls, v):
        if v is None:
            return v
            
        if len(v) < 8:
            raise ValueError('Пароль должен содержать минимум 8 символов')
        
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        
        if not (has_upper and has_lower and has_digit):
            raise ValueError('Пароль должен содержать заглавные и строчные буквы, а также цифры')
        
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "full_name": "Updated Administrator Name",
                "role": "moderator",
                "is_active": False,
                "notes": "Временно деактивирован"
            }
        }


class RefreshTokenRequest(BaseModel):
    """Запрос на обновление токена"""
    refresh_token: str = Field(..., description="Refresh токен")
    
    class Config:
        schema_extra = {
            "example": {
                "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
            }
        }


class ChangePasswordRequest(BaseModel):
    """Запрос на смену пароля"""
    current_password: str = Field(..., description="Текущий пароль")
    new_password: str = Field(..., min_length=8, description="Новый пароль")
    
    @validator('new_password')
    def validate_new_password(cls, v):
        if len(v) < 8:
            raise ValueError('Пароль должен содержать минимум 8 символов')
        
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        
        if not (has_upper and has_lower and has_digit):
            raise ValueError('Пароль должен содержать заглавные и строчные буквы, а также цифры')
        
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "current_password": "OldPassword123",
                "new_password": "NewSecurePass456"
            }
        }
