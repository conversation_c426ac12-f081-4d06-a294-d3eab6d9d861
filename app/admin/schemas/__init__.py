"""
Pydantic схемы для админ-панели

Содержит модели данных для API endpoints админ-панели.
"""

from .users import (
    UserResponse,
    UserCreate,
    UserUpdate,
    UserListResponse,
    UserDetailResponse,
    UserStatsResponse
)
from .auth import (
    AdminLoginRequest,
    AdminLoginResponse,
    AdminTokenResponse,
    AdminUserResponse,
    AdminUserCreate,
    AdminUserUpdate
)
from .common import (
    PaginationInfo,
    BaseResponse,
    ErrorResponse
)

__all__ = [
    # Users
    "UserResponse",
    "UserCreate", 
    "UserUpdate",
    "UserListResponse",
    "UserDetailResponse",
    "UserStatsResponse",
    
    # Auth
    "AdminLoginRequest",
    "AdminLoginResponse", 
    "AdminTokenResponse",
    "AdminUserResponse",
    "AdminUserCreate",
    "AdminUserUpdate",
    
    # Common
    "PaginationInfo",
    "BaseResponse",
    "ErrorResponse"
]
