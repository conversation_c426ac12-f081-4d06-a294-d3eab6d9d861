"""
Админ-панель для управления VPN сервисом

Модуль содержит веб-интерфейс для администраторов,
включая аутентификацию, управление пользователями,
подписками, платежами и мониторинг системы.
"""

from .auth import AdminAuth, get_current_admin_user
from .site import admin_site
from .models import AdminUser, AdminRole
from .dependencies import get_admin_db

__all__ = [
    "AdminAuth",
    "get_current_admin_user", 
    "admin_site",
    "AdminUser",
    "AdminRole",
    "get_admin_db"
]
