"""
Основной сайт админ-панели

Настройка FastAPI Amis Admin для управления VPN сервисом.
Включает кастомную аутентификацию и интеграцию с существующими моделями.
"""

from typing import Optional
from fastapi import FastAPI, Request, Depends
from fastapi_amis_admin.admin.settings import Settings
from fastapi_amis_admin.admin.site import AdminSite
from fastapi_amis_admin.admin import admin
from fastapi_amis_admin.amis.components import PageSchema, App
from sqlalchemy.ext.asyncio import AsyncEngine

from app.core.config import settings as app_settings
from app.database.database import engine
from .auth import get_current_admin_user
from .models import AdminUser


class VPNAdminSite(AdminSite):
    """
    Кастомный сайт админ-панели для VPN сервиса
    
    Расширяет базовый AdminSite для интеграции с нашей
    системой аутентификации и брендингом.
    """
    
    def __init__(self, settings: Settings, fastapi: Optional[FastAPI] = None, engine: Optional[AsyncEngine] = None):
        super().__init__(settings)
        
        # Отключаем стандартную аутентификацию Amis Admin
        # Будем использовать нашу собственную
        self.auth = None
    
    async def get_page(self, request: Request) -> App:
        """Кастомизация главной страницы админ-панели"""
        app = await super().get_page(request)
        
        # Настройка брендинга
        app.brandName = "Unveil VPN Admin"
        app.logo = "/static/admin/logo.png"
        
        # Кастомизация темы
        app.theme = "cxd"  # Современная тема
        
        # Добавляем информацию о пользователе в header
        try:
            # Пытаемся получить текущего администратора
            admin_user = await self._get_current_admin(request)
            if admin_user:
                app.header = {
                    "type": "tpl",
                    "tpl": f"Добро пожаловать, {admin_user.full_name or admin_user.username}! Роль: {admin_user.role.value}",
                    "className": "text-right p-2"
                }
        except:
            # Если не удалось получить пользователя, показываем базовый header
            pass
        
        return app
    
    async def _get_current_admin(self, request: Request) -> Optional[AdminUser]:
        """Получает текущего администратора из запроса"""
        try:
            # Здесь мы можем интегрироваться с нашей системой аутентификации
            # Пока возвращаем None, позже добавим интеграцию
            return None
        except:
            return None


# Создаем настройки для админ-панели
admin_settings = Settings(
    database_url_async=app_settings.database_url,
    site_title="Unveil VPN Admin Panel",
    site_description="Панель администрирования VPN сервиса",
    site_url=f"http://localhost:8000",  # В продакшене будет настоящий URL
    debug=app_settings.debug,
    language="ru",  # Русский язык по умолчанию
    amis_cdn="https://unpkg.com",  # CDN для Amis компонентов
    amis_pkg="amis@latest"
)

# Создаем экземпляр админ-сайта
admin_site = VPNAdminSite(
    settings=admin_settings,
    engine=engine
)


# Базовый класс для админ-страниц с проверкой прав доступа
class BaseVPNAdmin(admin.PageAdmin):
    """Базовый класс для всех админ-страниц VPN сервиса"""
    
    async def has_page_permission(self, request: Request) -> bool:
        """Проверка прав доступа к странице"""
        try:
            # Здесь будет интеграция с нашей системой аутентификации
            # Пока разрешаем доступ всем (для разработки)
            return True
        except:
            return False


# Главная страница админ-панели
@admin_site.register_admin
class DashboardAdmin(BaseVPNAdmin):
    """Главная страница с дашбордом"""
    page_schema = PageSchema(
        label="Дашборд", 
        icon="fa fa-dashboard",
        sort=1
    )
    
    async def get_page(self, request: Request):
        """Возвращает страницу дашборда"""
        from fastapi_amis_admin.amis.components import Page, Card, Grid, Flex
        
        return Page(
            title="Дашборд VPN сервиса",
            body=[
                Grid(
                    columns=[
                        {
                            "body": [
                                Card(
                                    header={"title": "Активные пользователи"},
                                    body="Здесь будет статистика активных пользователей"
                                )
                            ],
                            "md": 3
                        },
                        {
                            "body": [
                                Card(
                                    header={"title": "Доходы за месяц"},
                                    body="Здесь будет статистика доходов"
                                )
                            ],
                            "md": 3
                        },
                        {
                            "body": [
                                Card(
                                    header={"title": "Активные подписки"},
                                    body="Здесь будет статистика подписок"
                                )
                            ],
                            "md": 3
                        },
                        {
                            "body": [
                                Card(
                                    header={"title": "Серверы VPN"},
                                    body="Здесь будет статистика серверов"
                                )
                            ],
                            "md": 3
                        }
                    ]
                ),
                Card(
                    header={"title": "Последние транзакции"},
                    body="Здесь будет таблица последних транзакций"
                ),
                Card(
                    header={"title": "Системные уведомления"},
                    body="Здесь будут системные уведомления и алерты"
                )
            ]
        )


# Страница системной информации
@admin_site.register_admin
class SystemInfoAdmin(BaseVPNAdmin):
    """Страница с системной информацией"""
    page_schema = PageSchema(
        label="Система", 
        icon="fa fa-cogs",
        sort=100
    )
    
    async def get_page(self, request: Request):
        """Возвращает страницу с системной информацией"""
        from fastapi_amis_admin.amis.components import Page, Card
        import platform
        import psutil
        from datetime import datetime
        
        # Получаем системную информацию
        system_info = {
            "Операционная система": platform.system(),
            "Версия ОС": platform.release(),
            "Архитектура": platform.machine(),
            "Процессор": platform.processor(),
            "Python версия": platform.python_version(),
            "Время работы": str(datetime.now() - datetime.fromtimestamp(psutil.boot_time())),
            "Использование CPU": f"{psutil.cpu_percent()}%",
            "Использование RAM": f"{psutil.virtual_memory().percent}%",
            "Свободное место на диске": f"{psutil.disk_usage('/').free // (1024**3)} GB"
        }
        
        info_cards = []
        for key, value in system_info.items():
            info_cards.append(
                Card(
                    header={"title": key},
                    body=str(value)
                )
            )
        
        return Page(
            title="Системная информация",
            body=info_cards
        )


# Страница документации API
@admin_site.register_admin
class APIDocsAdmin(admin.IframeAdmin):
    """Страница документации API"""
    page_schema = PageSchema(
        label="API Документация", 
        icon="fa fa-book",
        sort=99
    )
    
    @property
    def src(self):
        """URL документации API"""
        return f"{admin_site.settings.site_url}/docs"


# Страница Redoc документации
@admin_site.register_admin
class ReDocAdmin(admin.IframeAdmin):
    """Страница Redoc документации"""
    page_schema = PageSchema(
        label="ReDoc", 
        icon="fa fa-file-text",
        sort=98
    )
    
    @property
    def src(self):
        """URL Redoc документации"""
        return f"{admin_site.settings.site_url}/redoc"


def setup_admin_site(app: FastAPI) -> None:
    """
    Настройка и подключение админ-панели к FastAPI приложению
    
    Args:
        app: Экземпляр FastAPI приложения
    """
    # Подключаем админ-сайт к приложению
    admin_site.mount_app(app)
    
    # Добавляем middleware для обработки статических файлов админ-панели
    from fastapi.staticfiles import StaticFiles
    import os
    
    # Создаем директорию для статических файлов админ-панели если её нет
    static_dir = "static/admin"
    os.makedirs(static_dir, exist_ok=True)
    
    # Подключаем статические файлы
    app.mount("/static/admin", StaticFiles(directory=static_dir), name="admin_static")


# Экспортируем для использования в других модулях
__all__ = [
    "admin_site",
    "VPNAdminSite", 
    "BaseVPNAdmin",
    "setup_admin_site"
]
