"""
Модели для админ-панели

Содержит модели для администраторов, ролей и прав доступа.
"""

import enum
from datetime import datetime, timezone
from typing import Optional, List
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, Table
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.ext.declarative import declarative_base

from app.database.models import Base


class AdminRole(enum.Enum):
    """Роли администраторов"""
    SUPER_ADMIN = "super_admin"  # Полный доступ ко всему
    ADMIN = "admin"              # Управление пользователями и подписками
    MODERATOR = "moderator"      # Просмотр данных и базовые операции
    SUPPORT = "support"          # Только поддержка пользователей
    VIEWER = "viewer"            # Только просмотр статистики


class AdminPermission(enum.Enum):
    """Разрешения для администраторов"""
    # Управление пользователями
    VIEW_USERS = "view_users"
    CREATE_USERS = "create_users"
    EDIT_USERS = "edit_users"
    DELETE_USERS = "delete_users"
    
    # Управление подписками
    VIEW_SUBSCRIPTIONS = "view_subscriptions"
    CREATE_SUBSCRIPTIONS = "create_subscriptions"
    EDIT_SUBSCRIPTIONS = "edit_subscriptions"
    DELETE_SUBSCRIPTIONS = "delete_subscriptions"
    
    # Управление платежами
    VIEW_PAYMENTS = "view_payments"
    PROCESS_REFUNDS = "process_refunds"
    VIEW_TRANSACTIONS = "view_transactions"
    
    # Управление тарифными планами
    VIEW_PLANS = "view_plans"
    CREATE_PLANS = "create_plans"
    EDIT_PLANS = "edit_plans"
    DELETE_PLANS = "delete_plans"
    
    # Системное администрирование
    VIEW_SYSTEM_STATS = "view_system_stats"
    MANAGE_SERVERS = "manage_servers"
    VIEW_LOGS = "view_logs"
    MANAGE_SETTINGS = "manage_settings"
    
    # Управление администраторами
    VIEW_ADMINS = "view_admins"
    CREATE_ADMINS = "create_admins"
    EDIT_ADMINS = "edit_admins"
    DELETE_ADMINS = "delete_admins"


# Таблица связи многие-ко-многим для ролей и разрешений
admin_role_permissions = Table(
    'admin_role_permissions',
    Base.metadata,
    Column('role', String, primary_key=True),
    Column('permission', String, primary_key=True)
)


class AdminUser(Base):
    """Модель администратора"""
    __tablename__ = "admin_users"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    username: Mapped[str] = mapped_column(String(50), unique=True, index=True, nullable=False)
    email: Mapped[str] = mapped_column(String(255), unique=True, index=True, nullable=False)
    hashed_password: Mapped[str] = mapped_column(String(255), nullable=False)
    full_name: Mapped[Optional[str]] = mapped_column(String(255))
    
    # Статус и роли
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    is_superuser: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    role: Mapped[AdminRole] = mapped_column(String(50), default=AdminRole.VIEWER, nullable=False)
    
    # Временные метки
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        default=lambda: datetime.now(timezone.utc),
        nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
        nullable=False
    )
    last_login: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # Дополнительная информация
    notes: Mapped[Optional[str]] = mapped_column(Text)
    created_by_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("admin_users.id"))
    
    # Связи
    created_by: Mapped[Optional["AdminUser"]] = relationship(
        "AdminUser", 
        remote_side=[id],
        back_populates="created_admins"
    )
    created_admins: Mapped[List["AdminUser"]] = relationship(
        "AdminUser",
        back_populates="created_by"
    )
    
    # Сессии
    sessions: Mapped[List["AdminSession"]] = relationship(
        "AdminSession",
        back_populates="admin_user",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<AdminUser(id={self.id}, username='{self.username}', role='{self.role.value}')>"
    
    def has_permission(self, permission: AdminPermission) -> bool:
        """Проверяет, есть ли у администратора определенное разрешение"""
        if self.is_superuser:
            return True
            
        role_permissions = get_role_permissions(self.role)
        return permission in role_permissions
    
    def has_any_permission(self, permissions: List[AdminPermission]) -> bool:
        """Проверяет, есть ли у администратора любое из указанных разрешений"""
        return any(self.has_permission(perm) for perm in permissions)
    
    def can_manage_user(self, target_admin: "AdminUser") -> bool:
        """Проверяет, может ли администратор управлять другим администратором"""
        if self.is_superuser:
            return True
            
        # Нельзя управлять самим собой через API
        if self.id == target_admin.id:
            return False
            
        # Нельзя управлять суперпользователями
        if target_admin.is_superuser:
            return False
            
        # Проверяем иерархию ролей
        role_hierarchy = {
            AdminRole.SUPER_ADMIN: 5,
            AdminRole.ADMIN: 4,
            AdminRole.MODERATOR: 3,
            AdminRole.SUPPORT: 2,
            AdminRole.VIEWER: 1
        }
        
        return role_hierarchy.get(self.role, 0) > role_hierarchy.get(target_admin.role, 0)


class AdminSession(Base):
    """Модель сессии администратора"""
    __tablename__ = "admin_sessions"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    admin_user_id: Mapped[int] = mapped_column(Integer, ForeignKey("admin_users.id"), nullable=False)
    session_token: Mapped[str] = mapped_column(String(255), unique=True, index=True, nullable=False)
    
    # Информация о сессии
    ip_address: Mapped[Optional[str]] = mapped_column(String(45))  # IPv6 поддержка
    user_agent: Mapped[Optional[str]] = mapped_column(Text)
    
    # Временные метки
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=lambda: datetime.now(timezone.utc),
        nullable=False
    )
    expires_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    last_activity: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=lambda: datetime.now(timezone.utc),
        nullable=False
    )
    
    # Статус
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    
    # Связи
    admin_user: Mapped[AdminUser] = relationship("AdminUser", back_populates="sessions")
    
    def __repr__(self) -> str:
        return f"<AdminSession(id={self.id}, admin_user_id={self.admin_user_id}, active={self.is_active})>"
    
    def is_expired(self) -> bool:
        """Проверяет, истекла ли сессия"""
        return datetime.now(timezone.utc) > self.expires_at
    
    def is_valid(self) -> bool:
        """Проверяет, валидна ли сессия"""
        return self.is_active and not self.is_expired()


def get_role_permissions(role: AdminRole) -> List[AdminPermission]:
    """Возвращает список разрешений для роли"""
    permissions_map = {
        AdminRole.SUPER_ADMIN: list(AdminPermission),  # Все разрешения
        
        AdminRole.ADMIN: [
            AdminPermission.VIEW_USERS,
            AdminPermission.CREATE_USERS,
            AdminPermission.EDIT_USERS,
            AdminPermission.DELETE_USERS,
            AdminPermission.VIEW_SUBSCRIPTIONS,
            AdminPermission.CREATE_SUBSCRIPTIONS,
            AdminPermission.EDIT_SUBSCRIPTIONS,
            AdminPermission.DELETE_SUBSCRIPTIONS,
            AdminPermission.VIEW_PAYMENTS,
            AdminPermission.PROCESS_REFUNDS,
            AdminPermission.VIEW_TRANSACTIONS,
            AdminPermission.VIEW_PLANS,
            AdminPermission.CREATE_PLANS,
            AdminPermission.EDIT_PLANS,
            AdminPermission.DELETE_PLANS,
            AdminPermission.VIEW_SYSTEM_STATS,
            AdminPermission.VIEW_LOGS,
        ],
        
        AdminRole.MODERATOR: [
            AdminPermission.VIEW_USERS,
            AdminPermission.EDIT_USERS,
            AdminPermission.VIEW_SUBSCRIPTIONS,
            AdminPermission.EDIT_SUBSCRIPTIONS,
            AdminPermission.VIEW_PAYMENTS,
            AdminPermission.VIEW_TRANSACTIONS,
            AdminPermission.VIEW_PLANS,
            AdminPermission.VIEW_SYSTEM_STATS,
        ],
        
        AdminRole.SUPPORT: [
            AdminPermission.VIEW_USERS,
            AdminPermission.EDIT_USERS,
            AdminPermission.VIEW_SUBSCRIPTIONS,
            AdminPermission.VIEW_PAYMENTS,
            AdminPermission.VIEW_TRANSACTIONS,
        ],
        
        AdminRole.VIEWER: [
            AdminPermission.VIEW_USERS,
            AdminPermission.VIEW_SUBSCRIPTIONS,
            AdminPermission.VIEW_PAYMENTS,
            AdminPermission.VIEW_TRANSACTIONS,
            AdminPermission.VIEW_PLANS,
            AdminPermission.VIEW_SYSTEM_STATS,
        ]
    }
    
    return permissions_map.get(role, [])
