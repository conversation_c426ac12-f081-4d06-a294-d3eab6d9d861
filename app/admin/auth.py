"""
Система аутентификации для админ-панели

Обеспечивает JWT-аутентификацию, управление сессиями
и проверку прав доступа для администраторов.
"""

import secrets
from datetime import datetime, timezone, timedelta
from typing import Optional, Dict, Any
from fastapi import HTTPException, status, Depends, Request
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from passlib.context import CryptContext
from jose import JWTError, jwt
import structlog

from app.core.config import settings
from app.database.database import get_db
from .models import AdminUser, AdminSession, AdminRole, AdminPermission

logger = structlog.get_logger()

# Настройка хеширования паролей
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Настройка JWT
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 8  # 8 часов
REFRESH_TOKEN_EXPIRE_DAYS = 30

# HTTP Bearer схема для токенов
security = HTTPBearer()


class AdminAuth:
    """Класс для управления аутентификацией администраторов"""
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """Проверяет пароль"""
        return pwd_context.verify(plain_password, hashed_password)
    
    @staticmethod
    def get_password_hash(password: str) -> str:
        """Хеширует пароль"""
        return pwd_context.hash(password)
    
    @staticmethod
    def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        """Создает JWT токен доступа"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.now(timezone.utc) + expires_delta
        else:
            expire = datetime.now(timezone.utc) + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        
        to_encode.update({"exp": expire, "type": "access"})
        encoded_jwt = jwt.encode(to_encode, settings.admin_secret_key, algorithm=ALGORITHM)
        return encoded_jwt
    
    @staticmethod
    def create_refresh_token(data: Dict[str, Any]) -> str:
        """Создает JWT refresh токен"""
        to_encode = data.copy()
        expire = datetime.now(timezone.utc) + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
        to_encode.update({"exp": expire, "type": "refresh"})
        encoded_jwt = jwt.encode(to_encode, settings.admin_secret_key, algorithm=ALGORITHM)
        return encoded_jwt
    
    @staticmethod
    def verify_token(token: str, token_type: str = "access") -> Optional[Dict[str, Any]]:
        """Проверяет JWT токен"""
        try:
            payload = jwt.decode(token, settings.admin_secret_key, algorithms=[ALGORITHM])
            
            # Проверяем тип токена
            if payload.get("type") != token_type:
                return None
                
            # Проверяем срок действия
            exp = payload.get("exp")
            if exp is None or datetime.fromtimestamp(exp, tz=timezone.utc) < datetime.now(timezone.utc):
                return None
                
            return payload
            
        except JWTError:
            return None
    
    @staticmethod
    async def authenticate_admin(
        db: AsyncSession, 
        username: str, 
        password: str
    ) -> Optional[AdminUser]:
        """Аутентифицирует администратора по логину и паролю"""
        try:
            # Ищем администратора по username или email
            stmt = select(AdminUser).where(
                (AdminUser.username == username) | (AdminUser.email == username)
            )
            result = await db.execute(stmt)
            admin = result.scalar_one_or_none()
            
            if not admin:
                logger.warning("Попытка входа с несуществующим пользователем", username=username)
                return None
            
            if not admin.is_active:
                logger.warning("Попытка входа неактивного администратора", admin_id=admin.id)
                return None
            
            if not AdminAuth.verify_password(password, admin.hashed_password):
                logger.warning("Неверный пароль для администратора", admin_id=admin.id)
                return None
            
            # Обновляем время последнего входа
            await db.execute(
                update(AdminUser)
                .where(AdminUser.id == admin.id)
                .values(last_login=datetime.now(timezone.utc))
            )
            await db.commit()
            
            logger.info("Успешная аутентификация администратора", admin_id=admin.id)
            return admin
            
        except Exception as e:
            logger.error("Ошибка аутентификации администратора", error=str(e))
            return None
    
    @staticmethod
    async def create_session(
        db: AsyncSession,
        admin: AdminUser,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> AdminSession:
        """Создает новую сессию администратора"""
        session_token = secrets.token_urlsafe(32)
        expires_at = datetime.now(timezone.utc) + timedelta(hours=ACCESS_TOKEN_EXPIRE_MINUTES // 60)
        
        session = AdminSession(
            admin_user_id=admin.id,
            session_token=session_token,
            ip_address=ip_address,
            user_agent=user_agent,
            expires_at=expires_at
        )
        
        db.add(session)
        await db.commit()
        await db.refresh(session)
        
        logger.info("Создана новая сессия администратора", admin_id=admin.id, session_id=session.id)
        return session
    
    @staticmethod
    async def get_admin_by_id(db: AsyncSession, admin_id: int) -> Optional[AdminUser]:
        """Получает администратора по ID"""
        stmt = select(AdminUser).where(AdminUser.id == admin_id)
        result = await db.execute(stmt)
        return result.scalar_one_or_none()
    
    @staticmethod
    async def invalidate_session(db: AsyncSession, session_token: str) -> bool:
        """Деактивирует сессию"""
        try:
            stmt = (
                update(AdminSession)
                .where(AdminSession.session_token == session_token)
                .values(is_active=False)
            )
            result = await db.execute(stmt)
            await db.commit()
            
            return result.rowcount > 0
            
        except Exception as e:
            logger.error("Ошибка деактивации сессии", error=str(e))
            return False
    
    @staticmethod
    async def cleanup_expired_sessions(db: AsyncSession) -> int:
        """Очищает истекшие сессии"""
        try:
            now = datetime.now(timezone.utc)
            stmt = (
                update(AdminSession)
                .where(AdminSession.expires_at < now)
                .values(is_active=False)
            )
            result = await db.execute(stmt)
            await db.commit()
            
            logger.info("Очищены истекшие сессии", count=result.rowcount)
            return result.rowcount
            
        except Exception as e:
            logger.error("Ошибка очистки сессий", error=str(e))
            return 0


async def get_current_admin_user(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> AdminUser:
    """Dependency для получения текущего аутентифицированного администратора"""
    
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Недействительные учетные данные",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # Проверяем токен
        payload = AdminAuth.verify_token(credentials.credentials)
        if payload is None:
            raise credentials_exception
        
        admin_id: int = payload.get("sub")
        if admin_id is None:
            raise credentials_exception
        
        # Получаем администратора из БД
        admin = await AdminAuth.get_admin_by_id(db, admin_id)
        if admin is None:
            raise credentials_exception
        
        if not admin.is_active:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Аккаунт администратора деактивирован"
            )
        
        # Логируем активность
        logger.debug(
            "Аутентифицированный запрос администратора",
            admin_id=admin.id,
            path=request.url.path,
            method=request.method
        )
        
        return admin
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Ошибка аутентификации администратора", error=str(e))
        raise credentials_exception


def require_permission(permission: AdminPermission):
    """Декоратор для проверки разрешений администратора"""
    def permission_checker(admin: AdminUser = Depends(get_current_admin_user)) -> AdminUser:
        if not admin.has_permission(permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Недостаточно прав доступа. Требуется: {permission.value}"
            )
        return admin
    
    return permission_checker


def require_role(role: AdminRole):
    """Декоратор для проверки роли администратора"""
    def role_checker(admin: AdminUser = Depends(get_current_admin_user)) -> AdminUser:
        if admin.role != role and not admin.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Недостаточно прав доступа. Требуется роль: {role.value}"
            )
        return admin
    
    return role_checker


def require_superuser():
    """Декоратор для проверки прав суперпользователя"""
    def superuser_checker(admin: AdminUser = Depends(get_current_admin_user)) -> AdminUser:
        if not admin.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Требуются права суперпользователя"
            )
        return admin
    
    return superuser_checker
