"""
Роутеры аутентификации для админ-панели

Обеспечивает endpoints для входа, выхода, обновления токенов
и управления сессиями администраторов.
"""

from datetime import timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession

from app.admin.dependencies import get_admin_db
from app.admin.auth import AdminAuth, get_current_admin_user, security
from app.admin.models import AdminUser
from app.admin.schemas.auth import (
    AdminLoginRequest,
    AdminLoginResponse,
    AdminTokenResponse,
    AdminUserResponse,
    RefreshTokenRequest,
    ChangePasswordRequest
)
from app.admin.schemas.common import SuccessResponse
import structlog

logger = structlog.get_logger()

router = APIRouter(prefix="/auth", tags=["admin-auth"])


@router.post("/login", response_model=AdminLoginResponse)
async def login(
    request: Request,
    login_data: AdminLoginRequest,
    db: AsyncSession = Depends(get_admin_db)
):
    """
    Вход в админ-панель
    
    Аутентифицирует администратора по логину/email и паролю,
    создает JWT токены и сессию.
    """
    
    # Получаем IP адрес и User-Agent
    client_ip = request.client.host if request.client else None
    user_agent = request.headers.get("User-Agent")
    
    # Аутентифицируем администратора
    admin = await AdminAuth.authenticate_admin(
        db, login_data.username, login_data.password
    )
    
    if not admin:
        logger.warning(
            "Неудачная попытка входа в админ-панель",
            username=login_data.username,
            ip=client_ip
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Неверные учетные данные"
        )
    
    # Создаем токены
    access_token = AdminAuth.create_access_token(
        data={"sub": admin.id, "username": admin.username, "role": admin.role.value}
    )
    refresh_token = AdminAuth.create_refresh_token(
        data={"sub": admin.id, "username": admin.username}
    )
    
    # Создаем сессию
    session = await AdminAuth.create_session(
        db, admin, ip_address=client_ip, user_agent=user_agent
    )
    
    logger.info(
        "Успешный вход в админ-панель",
        admin_id=admin.id,
        username=admin.username,
        ip=client_ip,
        session_id=session.id
    )
    
    # Формируем ответ
    user_response = AdminUserResponse.from_orm(admin)
    tokens_response = AdminTokenResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        token_type="bearer",
        expires_in=60 * 60 * 8  # 8 часов
    )
    
    return AdminLoginResponse(
        user=user_response,
        tokens=tokens_response
    )


@router.post("/refresh", response_model=AdminTokenResponse)
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    db: AsyncSession = Depends(get_admin_db)
):
    """
    Обновление токена доступа
    
    Использует refresh токен для получения нового access токена.
    """
    
    # Проверяем refresh токен
    payload = AdminAuth.verify_token(refresh_data.refresh_token, token_type="refresh")
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Недействительный refresh токен"
        )
    
    admin_id = payload.get("sub")
    if not admin_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Недействительный refresh токен"
        )
    
    # Получаем администратора
    admin = await AdminAuth.get_admin_by_id(db, admin_id)
    if not admin or not admin.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Администратор не найден или деактивирован"
        )
    
    # Создаем новые токены
    access_token = AdminAuth.create_access_token(
        data={"sub": admin.id, "username": admin.username, "role": admin.role.value}
    )
    new_refresh_token = AdminAuth.create_refresh_token(
        data={"sub": admin.id, "username": admin.username}
    )
    
    logger.info("Обновлен токен доступа", admin_id=admin.id)
    
    return AdminTokenResponse(
        access_token=access_token,
        refresh_token=new_refresh_token,
        token_type="bearer",
        expires_in=60 * 60 * 8  # 8 часов
    )


@router.post("/logout", response_model=SuccessResponse)
async def logout(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_admin_db)
):
    """
    Выход из админ-панели
    
    Деактивирует текущую сессию администратора.
    """
    
    # Проверяем токен
    payload = AdminAuth.verify_token(credentials.credentials)
    if payload:
        admin_id = payload.get("sub")
        if admin_id:
            # Деактивируем все сессии администратора
            # В реальном приложении можно деактивировать только текущую сессию
            await AdminAuth.cleanup_expired_sessions(db)
            
            logger.info("Выход из админ-панели", admin_id=admin_id)
    
    return SuccessResponse(message="Успешный выход из системы")


@router.get("/me", response_model=AdminUserResponse)
async def get_current_user(
    admin: AdminUser = Depends(get_current_admin_user)
):
    """
    Получение информации о текущем администраторе
    """
    return AdminUserResponse.from_orm(admin)


@router.post("/change-password", response_model=SuccessResponse)
async def change_password(
    password_data: ChangePasswordRequest,
    admin: AdminUser = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_admin_db)
):
    """
    Смена пароля текущего администратора
    """
    
    # Проверяем текущий пароль
    if not AdminAuth.verify_password(password_data.current_password, admin.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Неверный текущий пароль"
        )
    
    # Проверяем, что новый пароль отличается от текущего
    if AdminAuth.verify_password(password_data.new_password, admin.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Новый пароль должен отличаться от текущего"
        )
    
    # Обновляем пароль
    admin.hashed_password = AdminAuth.get_password_hash(password_data.new_password)
    await db.commit()
    
    logger.info("Пароль изменен", admin_id=admin.id)
    
    return SuccessResponse(message="Пароль успешно изменен")


@router.post("/validate-token", response_model=AdminUserResponse)
async def validate_token(
    admin: AdminUser = Depends(get_current_admin_user)
):
    """
    Валидация токена и получение информации об администраторе
    
    Используется для проверки действительности токена на фронтенде.
    """
    return AdminUserResponse.from_orm(admin)


@router.get("/permissions")
async def get_permissions(
    admin: AdminUser = Depends(get_current_admin_user)
):
    """
    Получение списка разрешений текущего администратора
    """
    from app.admin.models import AdminPermission, get_role_permissions
    
    if admin.is_superuser:
        permissions = [perm.value for perm in AdminPermission]
    else:
        role_permissions = get_role_permissions(admin.role)
        permissions = [perm.value for perm in role_permissions]
    
    return {
        "permissions": permissions,
        "role": admin.role.value,
        "is_superuser": admin.is_superuser
    }


@router.post("/cleanup-sessions", response_model=SuccessResponse)
async def cleanup_expired_sessions(
    db: AsyncSession = Depends(get_admin_db),
    admin: AdminUser = Depends(get_current_admin_user)
):
    """
    Очистка истекших сессий
    
    Доступно только суперпользователям.
    """
    if not admin.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Недостаточно прав доступа"
        )
    
    cleaned_count = await AdminAuth.cleanup_expired_sessions(db)
    
    logger.info("Очищены истекшие сессии", count=cleaned_count, admin_id=admin.id)
    
    return SuccessResponse(
        message=f"Очищено {cleaned_count} истекших сессий"
    )
