"""
Роутеры админ-панели

Содержит все API endpoints для управления VPN сервисом
через веб-интерфейс администратора.
"""

from fastapi import APIRouter
from .auth import router as auth_router
from .users import router as users_router

# Создаем главный роутер админ-панели
admin_router = APIRouter(prefix="/admin/api", tags=["admin"])

# Подключаем все роутеры
admin_router.include_router(auth_router)
admin_router.include_router(users_router)

__all__ = ["admin_router"]
