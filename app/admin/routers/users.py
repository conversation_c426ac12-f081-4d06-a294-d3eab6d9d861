"""
CRUD endpoints для управления пользователями в админ-панели

Предоставляет REST API для создания, чтения, обновления и удаления
пользователей VPN сервиса через админ-интерфейс.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from sqlalchemy.orm import selectinload

from app.database.models import User, UserSubscription, SubscriptionStatus
from app.admin.dependencies import (
    get_admin_db, 
    require_admin_permission,
    get_admin_pagination,
    get_admin_filters,
    AdminPagination,
    AdminFilters
)
from app.admin.models import AdminUser, AdminPermission
from app.admin.schemas.users import (
    UserResponse,
    UserCreate,
    UserUpdate,
    UserListResponse,
    UserDetailResponse,
    UserStatsResponse
)

router = APIRouter(prefix="/users", tags=["admin-users"])


@router.get("/", response_model=UserListResponse)
async def list_users(
    db: AsyncSession = Depends(get_admin_db),
    pagination: AdminPagination = Depends(get_admin_pagination),
    filters: AdminFilters = Depends(get_admin_filters),
    status_filter: Optional[str] = Query(None, description="Фильтр по статусу (active, inactive)"),
    subscription_status: Optional[str] = Query(None, description="Фильтр по статусу подписки"),
    admin: AdminUser = Depends(require_admin_permission(AdminPermission.VIEW_USERS))
):
    """
    Получение списка пользователей с пагинацией и фильтрацией
    
    Доступные фильтры:
    - search: Поиск по username, email, full_name
    - status_filter: active/inactive
    - subscription_status: active/expired/cancelled
    - sort_by: username, email, created_at, last_login
    """
    
    # Базовый запрос
    query = select(User).options(
        selectinload(User.subscriptions)
    )
    
    # Применяем фильтры
    conditions = []
    
    # Поиск по тексту
    if filters.has_search():
        search_term = f"%{filters.search}%"
        conditions.append(
            or_(
                User.username.ilike(search_term),
                User.email.ilike(search_term),
                User.full_name.ilike(search_term)
            )
        )
    
    # Фильтр по статусу пользователя
    if status_filter:
        if status_filter == "active":
            conditions.append(User.is_active == True)
        elif status_filter == "inactive":
            conditions.append(User.is_active == False)
    
    # Фильтр по статусу подписки
    if subscription_status:
        if subscription_status == "active":
            query = query.join(UserSubscription).where(
                UserSubscription.status == SubscriptionStatus.ACTIVE
            )
        elif subscription_status == "expired":
            query = query.join(UserSubscription).where(
                UserSubscription.status == SubscriptionStatus.EXPIRED
            )
        elif subscription_status == "cancelled":
            query = query.join(UserSubscription).where(
                UserSubscription.status == SubscriptionStatus.CANCELLED
            )
    
    if conditions:
        query = query.where(and_(*conditions))
    
    # Сортировка
    if filters.sort_by:
        sort_column = getattr(User, filters.sort_by, None)
        if sort_column:
            order_func = filters.get_order_direction()
            query = query.order_by(order_func(sort_column))
    else:
        query = query.order_by(User.created_at.desc())
    
    # Подсчет общего количества
    count_query = select(func.count()).select_from(query.subquery())
    total_count = await db.scalar(count_query)
    
    # Применяем пагинацию
    query = query.offset(pagination.offset).limit(pagination.limit)
    
    # Выполняем запрос
    result = await db.execute(query)
    users = result.scalars().all()
    
    # Формируем ответ
    return UserListResponse(
        users=[UserResponse.from_orm(user) for user in users],
        pagination=pagination.get_pagination_info(total_count)
    )


@router.get("/stats", response_model=UserStatsResponse)
async def get_users_stats(
    db: AsyncSession = Depends(get_admin_db),
    admin: AdminUser = Depends(require_admin_permission(AdminPermission.VIEW_USERS))
):
    """Получение статистики по пользователям"""
    
    # Общее количество пользователей
    total_users = await db.scalar(select(func.count(User.id)))
    
    # Активные пользователи
    active_users = await db.scalar(
        select(func.count(User.id)).where(User.is_active == True)
    )
    
    # Пользователи с активными подписками
    users_with_active_subs = await db.scalar(
        select(func.count(func.distinct(UserSubscription.user_id)))
        .where(UserSubscription.status == SubscriptionStatus.ACTIVE)
    )
    
    # Новые пользователи за последние 30 дней
    from datetime import datetime, timezone, timedelta
    thirty_days_ago = datetime.now(timezone.utc) - timedelta(days=30)
    new_users_30d = await db.scalar(
        select(func.count(User.id)).where(User.created_at >= thirty_days_ago)
    )
    
    return UserStatsResponse(
        total_users=total_users,
        active_users=active_users,
        inactive_users=total_users - active_users,
        users_with_active_subscriptions=users_with_active_subs,
        new_users_last_30_days=new_users_30d
    )


@router.get("/{user_id}", response_model=UserDetailResponse)
async def get_user(
    user_id: int,
    db: AsyncSession = Depends(get_admin_db),
    admin: AdminUser = Depends(require_admin_permission(AdminPermission.VIEW_USERS))
):
    """Получение детальной информации о пользователе"""
    
    query = select(User).options(
        selectinload(User.subscriptions),
        selectinload(User.transactions)
    ).where(User.id == user_id)
    
    result = await db.execute(query)
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Пользователь не найден"
        )
    
    return UserDetailResponse.from_orm(user)


@router.post("/", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def create_user(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_admin_db),
    admin: AdminUser = Depends(require_admin_permission(AdminPermission.CREATE_USERS))
):
    """Создание нового пользователя"""
    
    # Проверяем уникальность username и email
    existing_user = await db.scalar(
        select(User).where(
            or_(User.username == user_data.username, User.email == user_data.email)
        )
    )
    
    if existing_user:
        if existing_user.username == user_data.username:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Пользователь с таким username уже существует"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Пользователь с таким email уже существует"
            )
    
    # Создаем пользователя
    user = User(
        telegram_id=user_data.telegram_id,
        username=user_data.username,
        email=user_data.email,
        full_name=user_data.full_name,
        is_active=user_data.is_active,
        language_code=user_data.language_code
    )
    
    db.add(user)
    await db.commit()
    await db.refresh(user)
    
    return UserResponse.from_orm(user)


@router.put("/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: int,
    user_data: UserUpdate,
    db: AsyncSession = Depends(get_admin_db),
    admin: AdminUser = Depends(require_admin_permission(AdminPermission.EDIT_USERS))
):
    """Обновление информации о пользователе"""
    
    # Получаем пользователя
    user = await db.scalar(select(User).where(User.id == user_id))
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Пользователь не найден"
        )
    
    # Проверяем уникальность при изменении username или email
    if user_data.username and user_data.username != user.username:
        existing = await db.scalar(
            select(User).where(
                and_(User.username == user_data.username, User.id != user_id)
            )
        )
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Пользователь с таким username уже существует"
            )
    
    if user_data.email and user_data.email != user.email:
        existing = await db.scalar(
            select(User).where(
                and_(User.email == user_data.email, User.id != user_id)
            )
        )
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Пользователь с таким email уже существует"
            )
    
    # Обновляем поля
    update_data = user_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(user, field, value)
    
    await db.commit()
    await db.refresh(user)
    
    return UserResponse.from_orm(user)


@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(
    user_id: int,
    db: AsyncSession = Depends(get_admin_db),
    admin: AdminUser = Depends(require_admin_permission(AdminPermission.DELETE_USERS))
):
    """Удаление пользователя"""
    
    user = await db.scalar(select(User).where(User.id == user_id))
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Пользователь не найден"
        )
    
    # Проверяем, есть ли активные подписки
    active_subscription = await db.scalar(
        select(UserSubscription).where(
            and_(
                UserSubscription.user_id == user_id,
                UserSubscription.status == SubscriptionStatus.ACTIVE
            )
        )
    )
    
    if active_subscription:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Нельзя удалить пользователя с активной подпиской"
        )
    
    await db.delete(user)
    await db.commit()


@router.post("/{user_id}/activate", response_model=UserResponse)
async def activate_user(
    user_id: int,
    db: AsyncSession = Depends(get_admin_db),
    admin: AdminUser = Depends(require_admin_permission(AdminPermission.EDIT_USERS))
):
    """Активация пользователя"""
    
    user = await db.scalar(select(User).where(User.id == user_id))
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Пользователь не найден"
        )
    
    user.is_active = True
    await db.commit()
    await db.refresh(user)
    
    return UserResponse.from_orm(user)


@router.post("/{user_id}/deactivate", response_model=UserResponse)
async def deactivate_user(
    user_id: int,
    db: AsyncSession = Depends(get_admin_db),
    admin: AdminUser = Depends(require_admin_permission(AdminPermission.EDIT_USERS))
):
    """Деактивация пользователя"""
    
    user = await db.scalar(select(User).where(User.id == user_id))
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Пользователь не найден"
        )
    
    user.is_active = False
    await db.commit()
    await db.refresh(user)
    
    return UserResponse.from_orm(user)
