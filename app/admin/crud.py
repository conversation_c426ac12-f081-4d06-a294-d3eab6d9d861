"""
CRUD операции для админ-панели

Содержит функции для работы с администраторами,
сессиями и другими сущностями админ-панели.
"""

from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func, and_, or_
from sqlalchemy.orm import selectinload

from .models import AdminUser, AdminSession, AdminRole
from .auth import AdminAuth


async def create_admin_user(
    db: AsyncSession,
    username: str,
    email: str,
    password: str,
    full_name: Optional[str] = None,
    role: AdminRole = AdminRole.VIEWER,
    is_active: bool = True,
    is_superuser: bool = False,
    created_by_id: Optional[int] = None,
    notes: Optional[str] = None
) -> AdminUser:
    """Создает нового администратора"""
    
    # Проверяем уникальность
    existing = await db.scalar(
        select(AdminUser).where(
            or_(AdminUser.username == username, AdminUser.email == email)
        )
    )
    
    if existing:
        if existing.username == username:
            raise ValueError("Администратор с таким username уже существует")
        else:
            raise ValueError("Администратор с таким email уже существует")
    
    # Хешируем пароль
    hashed_password = AdminAuth.get_password_hash(password)
    
    # Создаем администратора
    admin = AdminUser(
        username=username,
        email=email,
        hashed_password=hashed_password,
        full_name=full_name,
        role=role.value if hasattr(role, 'value') else role,
        is_active=is_active,
        is_superuser=is_superuser,
        created_by_id=created_by_id,
        notes=notes
    )
    
    db.add(admin)
    await db.commit()
    await db.refresh(admin)
    
    return admin


async def get_admin_user_by_id(db: AsyncSession, admin_id: int) -> Optional[AdminUser]:
    """Получает администратора по ID"""
    return await db.scalar(
        select(AdminUser).where(AdminUser.id == admin_id)
    )


async def get_admin_user_by_username(db: AsyncSession, username: str) -> Optional[AdminUser]:
    """Получает администратора по username"""
    return await db.scalar(
        select(AdminUser).where(AdminUser.username == username)
    )


async def get_admin_user_by_email(db: AsyncSession, email: str) -> Optional[AdminUser]:
    """Получает администратора по email"""
    return await db.scalar(
        select(AdminUser).where(AdminUser.email == email)
    )


async def get_admin_users(
    db: AsyncSession,
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    role: Optional[AdminRole] = None,
    is_active: Optional[bool] = None
) -> List[AdminUser]:
    """Получает список администраторов с фильтрацией"""
    
    query = select(AdminUser).options(
        selectinload(AdminUser.created_by),
        selectinload(AdminUser.sessions)
    )
    
    # Применяем фильтры
    conditions = []
    
    if search:
        search_term = f"%{search}%"
        conditions.append(
            or_(
                AdminUser.username.ilike(search_term),
                AdminUser.email.ilike(search_term),
                AdminUser.full_name.ilike(search_term)
            )
        )
    
    if role:
        conditions.append(AdminUser.role == role)
    
    if is_active is not None:
        conditions.append(AdminUser.is_active == is_active)
    
    if conditions:
        query = query.where(and_(*conditions))
    
    query = query.order_by(AdminUser.created_at.desc())
    query = query.offset(skip).limit(limit)
    
    result = await db.execute(query)
    return result.scalars().all()


async def count_admin_users(
    db: AsyncSession,
    search: Optional[str] = None,
    role: Optional[AdminRole] = None,
    is_active: Optional[bool] = None
) -> int:
    """Подсчитывает количество администраторов с фильтрацией"""
    
    query = select(func.count(AdminUser.id))
    
    # Применяем те же фильтры
    conditions = []
    
    if search:
        search_term = f"%{search}%"
        conditions.append(
            or_(
                AdminUser.username.ilike(search_term),
                AdminUser.email.ilike(search_term),
                AdminUser.full_name.ilike(search_term)
            )
        )
    
    if role:
        conditions.append(AdminUser.role == role)
    
    if is_active is not None:
        conditions.append(AdminUser.is_active == is_active)
    
    if conditions:
        query = query.where(and_(*conditions))
    
    return await db.scalar(query)


async def update_admin_user(
    db: AsyncSession,
    admin_id: int,
    **update_data
) -> Optional[AdminUser]:
    """Обновляет данные администратора"""
    
    # Если обновляется пароль, хешируем его
    if "password" in update_data:
        update_data["hashed_password"] = AdminAuth.get_password_hash(update_data.pop("password"))
    
    # Выполняем обновление
    stmt = (
        update(AdminUser)
        .where(AdminUser.id == admin_id)
        .values(**update_data)
        .returning(AdminUser)
    )
    
    result = await db.execute(stmt)
    await db.commit()
    
    return result.scalar_one_or_none()


async def delete_admin_user(db: AsyncSession, admin_id: int) -> bool:
    """Удаляет администратора"""
    
    # Сначала удаляем все сессии
    await db.execute(
        delete(AdminSession).where(AdminSession.admin_user_id == admin_id)
    )
    
    # Затем удаляем администратора
    result = await db.execute(
        delete(AdminUser).where(AdminUser.id == admin_id)
    )
    
    await db.commit()
    return result.rowcount > 0


async def activate_admin_user(db: AsyncSession, admin_id: int) -> Optional[AdminUser]:
    """Активирует администратора"""
    return await update_admin_user(db, admin_id, is_active=True)


async def deactivate_admin_user(db: AsyncSession, admin_id: int) -> Optional[AdminUser]:
    """Деактивирует администратора"""
    return await update_admin_user(db, admin_id, is_active=False)


async def get_admin_sessions(
    db: AsyncSession,
    admin_id: Optional[int] = None,
    active_only: bool = True
) -> List[AdminSession]:
    """Получает сессии администраторов"""
    
    query = select(AdminSession).options(
        selectinload(AdminSession.admin_user)
    )
    
    conditions = []
    
    if admin_id:
        conditions.append(AdminSession.admin_user_id == admin_id)
    
    if active_only:
        conditions.append(AdminSession.is_active == True)
    
    if conditions:
        query = query.where(and_(*conditions))
    
    query = query.order_by(AdminSession.created_at.desc())
    
    result = await db.execute(query)
    return result.scalars().all()


async def invalidate_admin_session(db: AsyncSession, session_id: int) -> bool:
    """Деактивирует сессию администратора"""
    
    result = await db.execute(
        update(AdminSession)
        .where(AdminSession.id == session_id)
        .values(is_active=False)
    )
    
    await db.commit()
    return result.rowcount > 0


async def invalidate_admin_sessions_by_user(db: AsyncSession, admin_id: int) -> int:
    """Деактивирует все сессии администратора"""
    
    result = await db.execute(
        update(AdminSession)
        .where(AdminSession.admin_user_id == admin_id)
        .values(is_active=False)
    )
    
    await db.commit()
    return result.rowcount


async def cleanup_expired_sessions(db: AsyncSession) -> int:
    """Очищает истекшие сессии"""
    from datetime import datetime, timezone
    
    now = datetime.now(timezone.utc)
    
    result = await db.execute(
        update(AdminSession)
        .where(AdminSession.expires_at < now)
        .values(is_active=False)
    )
    
    await db.commit()
    return result.rowcount


async def get_admin_stats(db: AsyncSession) -> dict:
    """Получает статистику по администраторам"""
    
    # Общее количество администраторов
    total_admins = await db.scalar(select(func.count(AdminUser.id)))
    
    # Активные администраторы
    active_admins = await db.scalar(
        select(func.count(AdminUser.id)).where(AdminUser.is_active == True)
    )
    
    # Суперпользователи
    superusers = await db.scalar(
        select(func.count(AdminUser.id)).where(AdminUser.is_superuser == True)
    )
    
    # Статистика по ролям
    role_stats = {}
    for role in AdminRole:
        count = await db.scalar(
            select(func.count(AdminUser.id)).where(AdminUser.role == role)
        )
        role_stats[role.value] = count
    
    # Активные сессии
    active_sessions = await db.scalar(
        select(func.count(AdminSession.id)).where(AdminSession.is_active == True)
    )
    
    return {
        "total_admins": total_admins,
        "active_admins": active_admins,
        "inactive_admins": total_admins - active_admins,
        "superusers": superusers,
        "role_distribution": role_stats,
        "active_sessions": active_sessions
    }
