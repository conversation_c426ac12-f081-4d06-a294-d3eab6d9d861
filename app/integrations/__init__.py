"""
Модуль интеграций с внешними сервисами
"""

# Интеграция с Marzban VPN панелью
from .marzban import (
    MarzbanClient,
    MarzbanConnectionConfig,
    MarzbanUserCreate,
    MarzbanUserUpdate,
    MarzbanUserResponse,
    MarzbanUserStatus,
    MarzbanProtocol,
    MarzbanSystemStats,
    MarzbanUserUsageResponse,
    MarzbanException,
    MarzbanConnectionError,
    MarzbanAuthenticationError,
    MarzbanUserNotFoundError,
    MarzbanUserAlreadyExistsError,
    MarzbanQuotaExceededError,
    MarzbanConfigurationError,
    MarzbanAPIError,
)

__all__ = [
    # Marzban
    "MarzbanClient",
    "MarzbanConnectionConfig",
    "MarzbanUserCreate",
    "MarzbanUserUpdate",
    "MarzbanUserResponse",
    "MarzbanUserStatus",
    "MarzbanProtocol",
    "MarzbanSystemStats",
    "MarzbanUserUsageResponse",
    "MarzbanException",
    "MarzbanConnectionError",
    "MarzbanAuthenticationError",
    "MarzbanUserNotFoundError",
    "MarzbanUserAlreadyExistsError",
    "MarzbanQuotaExceededError",
    "MarzbanConfigurationError",
    "MarzbanAPIError",
]
