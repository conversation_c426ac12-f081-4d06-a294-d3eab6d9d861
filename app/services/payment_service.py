"""
Сервис управления платежами - единая точка входа для всех платежных провайдеров
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from decimal import Decimal
from enum import Enum
import structlog
from datetime import datetime, timezone

from app.database.models import Transaction, TransactionStatus, User
from app.database.database import AsyncSession

logger = structlog.get_logger()


class PaymentProvider(str, Enum):
    """Поддерживаемые платежные провайдеры"""
    YOOKASSA = "yookassa"
    CRYPTOMUS = "cryptomus"
    TELEGRAM_STARS = "telegram_stars"


class PaymentStatus(str, Enum):
    """Статусы платежей"""
    PENDING = "pending"
    WAITING_FOR_CAPTURE = "waiting_for_capture"
    SUCCEEDED = "succeeded"
    CANCELED = "canceled"
    FAILED = "failed"


class PaymentMethod(str, Enum):
    """Методы оплаты"""
    BANK_CARD = "bank_card"
    CRYPTO = "crypto"
    TELEGRAM_STARS = "telegram_stars"
    QIWI = "qiwi"
    YOOMONEY = "yoomoney"


class PaymentResult:
    """Результат создания платежа"""
    
    def __init__(
        self,
        payment_id: str,
        status: PaymentStatus,
        payment_url: Optional[str] = None,
        amount: Optional[Decimal] = None,
        currency: Optional[str] = None,
        provider: Optional[PaymentProvider] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        self.payment_id = payment_id
        self.status = status
        self.payment_url = payment_url
        self.amount = amount
        self.currency = currency
        self.provider = provider
        self.metadata = metadata or {}
        self.created_at = datetime.now(timezone.utc)
    
    def to_dict(self) -> Dict[str, Any]:
        """Преобразует результат в словарь"""
        return {
            "payment_id": self.payment_id,
            "status": self.status.value,
            "payment_url": self.payment_url,
            "amount": float(self.amount) if self.amount else None,
            "currency": self.currency,
            "provider": self.provider.value if self.provider else None,
            "metadata": self.metadata,
            "created_at": self.created_at.isoformat()
        }


class PaymentProviderInterface(ABC):
    """Абстрактный интерфейс для платежных провайдеров"""
    
    @abstractmethod
    async def create_payment(
        self,
        amount: Decimal,
        currency: str,
        description: str,
        return_url: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> PaymentResult:
        """
        Создает платеж
        
        Args:
            amount: Сумма платежа
            currency: Валюта платежа
            description: Описание платежа
            return_url: URL для возврата после оплаты
            metadata: Дополнительные данные
            
        Returns:
            PaymentResult: Результат создания платежа
        """
        pass
    
    @abstractmethod
    async def get_payment_status(self, payment_id: str) -> PaymentResult:
        """
        Получает статус платежа
        
        Args:
            payment_id: ID платежа
            
        Returns:
            PaymentResult: Текущий статус платежа
        """
        pass
    
    @abstractmethod
    async def cancel_payment(self, payment_id: str) -> PaymentResult:
        """
        Отменяет платеж
        
        Args:
            payment_id: ID платежа
            
        Returns:
            PaymentResult: Результат отмены
        """
        pass
    
    @abstractmethod
    async def refund_payment(
        self,
        payment_id: str,
        amount: Optional[Decimal] = None,
        reason: Optional[str] = None
    ) -> PaymentResult:
        """
        Возвращает платеж
        
        Args:
            payment_id: ID платежа
            amount: Сумма возврата (None = полный возврат)
            reason: Причина возврата
            
        Returns:
            PaymentResult: Результат возврата
        """
        pass
    
    @abstractmethod
    def validate_webhook(self, headers: Dict[str, str], body: bytes) -> bool:
        """
        Валидирует webhook от платежного провайдера
        
        Args:
            headers: HTTP заголовки
            body: Тело запроса
            
        Returns:
            bool: True если webhook валидный
        """
        pass
    
    @abstractmethod
    async def process_webhook(
        self,
        headers: Dict[str, str],
        body: Dict[str, Any]
    ) -> Optional[PaymentResult]:
        """
        Обрабатывает webhook от платежного провайдера
        
        Args:
            headers: HTTP заголовки
            body: Тело запроса
            
        Returns:
            PaymentResult: Обновленный статус платежа или None
        """
        pass


class PaymentService:
    """Основной сервис управления платежами"""
    
    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session
        self.providers: Dict[PaymentProvider, PaymentProviderInterface] = {}
        
    def register_provider(
        self,
        provider_type: PaymentProvider,
        provider: PaymentProviderInterface
    ) -> None:
        """
        Регистрирует платежного провайдера
        
        Args:
            provider_type: Тип провайдера
            provider: Экземпляр провайдера
        """
        self.providers[provider_type] = provider
        logger.info("Зарегистрирован платежный провайдер", provider=provider_type.value)
    
    def get_available_providers(self) -> List[PaymentProvider]:
        """Возвращает список доступных провайдеров"""
        return list(self.providers.keys())
    
    async def create_payment(
        self,
        provider: PaymentProvider,
        amount: Decimal,
        currency: str,
        description: str,
        user_id: int,
        subscription_plan_id: Optional[int] = None,
        return_url: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> PaymentResult:
        """
        Создает платеж через указанного провайдера
        
        Args:
            provider: Платежный провайдер
            amount: Сумма платежа
            currency: Валюта
            description: Описание
            user_id: ID пользователя
            subscription_plan_id: ID тарифного плана
            return_url: URL возврата
            metadata: Дополнительные данные
            
        Returns:
            PaymentResult: Результат создания платежа
            
        Raises:
            ValueError: Если провайдер не поддерживается
        """
        if provider not in self.providers:
            raise ValueError(f"Провайдер {provider.value} не поддерживается")
        
        try:
            # Создаем платеж через провайдера
            provider_instance = self.providers[provider]
            payment_metadata = metadata or {}
            payment_metadata.update({
                "user_id": user_id,
                "subscription_plan_id": subscription_plan_id
            })
            
            result = await provider_instance.create_payment(
                amount=amount,
                currency=currency,
                description=description,
                return_url=return_url,
                metadata=payment_metadata
            )
            
            # Сохраняем транзакцию в БД
            await self._save_transaction(
                payment_id=result.payment_id,
                provider=provider,
                amount=amount,
                currency=currency,
                user_id=user_id,
                subscription_plan_id=subscription_plan_id,
                status=self._map_payment_status_to_transaction_status(result.status),
                metadata=result.metadata
            )
            
            logger.info(
                "Создан платеж",
                payment_id=result.payment_id,
                provider=provider.value,
                amount=float(amount),
                currency=currency,
                user_id=user_id
            )
            
            return result
            
        except Exception as e:
            logger.error(
                "Ошибка создания платежа",
                error=str(e),
                provider=provider.value,
                user_id=user_id
            )
            raise
    
    async def get_payment_status(
        self,
        payment_id: str,
        provider: PaymentProvider
    ) -> PaymentResult:
        """
        Получает статус платежа
        
        Args:
            payment_id: ID платежа
            provider: Платежный провайдер
            
        Returns:
            PaymentResult: Статус платежа
        """
        if provider not in self.providers:
            raise ValueError(f"Провайдер {provider.value} не поддерживается")
        
        provider_instance = self.providers[provider]
        return await provider_instance.get_payment_status(payment_id)
    
    async def process_webhook(
        self,
        provider: PaymentProvider,
        headers: Dict[str, str],
        body: Dict[str, Any]
    ) -> Optional[PaymentResult]:
        """
        Обрабатывает webhook от платежного провайдера
        
        Args:
            provider: Платежный провайдер
            headers: HTTP заголовки
            body: Тело запроса
            
        Returns:
            PaymentResult: Обновленный статус или None
        """
        if provider not in self.providers:
            logger.warning("Получен webhook от неподдерживаемого провайдера", provider=provider.value)
            return None
        
        provider_instance = self.providers[provider]
        
        # Валидируем webhook
        if not provider_instance.validate_webhook(headers, body):
            logger.warning("Невалидный webhook", provider=provider.value)
            return None
        
        # Обрабатываем webhook
        result = await provider_instance.process_webhook(headers, body)
        
        if result:
            # Обновляем статус в БД
            await self._update_transaction_status(
                payment_id=result.payment_id,
                status=self._map_payment_status_to_transaction_status(result.status),
                metadata=result.metadata
            )
            
            logger.info(
                "Обработан webhook",
                payment_id=result.payment_id,
                provider=provider.value,
                status=result.status.value
            )
        
        return result
    
    async def _save_transaction(
        self,
        payment_id: str,
        provider: PaymentProvider,
        amount: Decimal,
        currency: str,
        user_id: int,
        subscription_plan_id: Optional[int],
        status: TransactionStatus,
        metadata: Dict[str, Any]
    ) -> None:
        """Сохраняет транзакцию в базе данных"""
        from app.database.crud import create_transaction
        
        transaction_data = {
            "payment_id": payment_id,
            "payment_system": provider.value,
            "amount": amount,
            "currency": currency,
            "user_id": user_id,
            "subscription_id": subscription_plan_id,
            "status": status,
            "metadata": metadata
        }
        
        await create_transaction(self.db_session, transaction_data)
    
    async def _update_transaction_status(
        self,
        payment_id: str,
        status: TransactionStatus,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Обновляет статус транзакции"""
        from app.database.crud import update_transaction_by_payment_id
        
        update_data = {"status": status}
        if metadata:
            update_data["metadata"] = metadata
        
        await update_transaction_by_payment_id(self.db_session, payment_id, update_data)
    
    def _map_payment_status_to_transaction_status(self, payment_status: PaymentStatus) -> TransactionStatus:
        """Маппинг статусов платежа в статусы транзакции"""
        mapping = {
            PaymentStatus.PENDING: TransactionStatus.PENDING,
            PaymentStatus.WAITING_FOR_CAPTURE: TransactionStatus.PENDING,
            PaymentStatus.SUCCEEDED: TransactionStatus.COMPLETED,
            PaymentStatus.CANCELED: TransactionStatus.CANCELLED,
            PaymentStatus.FAILED: TransactionStatus.FAILED
        }
        return mapping.get(payment_status, TransactionStatus.PENDING)
