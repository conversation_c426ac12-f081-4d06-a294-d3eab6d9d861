"""
Сервис управления подписками

Обеспечивает бизнес-логику для работы с подписками пользователей,
интеграцию с Marzban API и автоматическое управление жизненным циклом подписок.
"""

import asyncio
from datetime import datetime, timezone, timedelta
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
import structlog

from app.database.models import (
    User, SubscriptionPlan, UserSubscription, 
    SubscriptionStatus, Transaction, TransactionStatus
)
from app.database.crud import (
    get_user_by_telegram_id, get_subscription_plan_by_id,
    create_user_subscription, get_user_subscriptions,
    update_user_subscription
)
from app.integrations.marzban import (
    MarzbanClient, MarzbanUserCreate, <PERSON><PERSON>banUserUpdate, 
    <PERSON><PERSON>banUserStatus, MarzbanUserNotFoundError,
    MarzbanUserAlreadyExistsError, MarzbanException
)
from app.core.config import settings
from app.utils.helpers import create_marzban_username, format_bytes

logger = structlog.get_logger()


class SubscriptionService:
    """
    Сервис управления подписками
    
    Следует принципам A2A:
    - Async First: Полностью асинхронная реализация
    - Enterprise Ready: Логирование, обработка ошибок, транзакции
    - Opaque Execution: Инкапсуляция бизнес-логики
    """
    
    def __init__(self, db_session: AsyncSession, marzban_client: Optional[MarzbanClient] = None):
        """
        Инициализация сервиса
        
        Args:
            db_session: Сессия базы данных
            marzban_client: Клиент Marzban API (опционально)
        """
        self.db = db_session
        self.marzban_client = marzban_client or MarzbanClient()
        
    async def create_subscription(
        self, 
        user_telegram_id: int, 
        plan_id: int,
        transaction_id: Optional[int] = None
    ) -> UserSubscription:
        """
        Создает новую подписку для пользователя
        
        Args:
            user_telegram_id: Telegram ID пользователя
            plan_id: ID тарифного плана
            transaction_id: ID транзакции оплаты (опционально)
            
        Returns:
            Созданная подписка
            
        Raises:
            ValueError: Если пользователь или план не найдены
            MarzbanException: Ошибки интеграции с Marzban
        """
        logger.info(
            "Создание подписки",
            user_telegram_id=user_telegram_id,
            plan_id=plan_id,
            transaction_id=transaction_id
        )
        
        # Получаем пользователя и план
        user = await get_user_by_telegram_id(self.db, user_telegram_id)
        if not user:
            raise ValueError(f"Пользователь {user_telegram_id} не найден")
            
        plan = await get_subscription_plan_by_id(self.db, plan_id)
        if not plan:
            raise ValueError(f"План {plan_id} не найден")
            
        # Проверяем активные подписки
        active_subscriptions = await self._get_active_subscriptions(user.id)
        if active_subscriptions:
            logger.warning(
                "У пользователя уже есть активные подписки",
                user_id=user.id,
                active_count=len(active_subscriptions)
            )
        
        # Создаем подписку в базе данных
        subscription_data = {
            'user_id': user.id,
            'plan_id': plan.id,
            'status': SubscriptionStatus.PENDING,
            'start_date': datetime.now(timezone.utc),
            'end_date': datetime.now(timezone.utc) + timedelta(days=plan.duration_days),
            'traffic_limit': plan.traffic_limit,
            'traffic_used': 0,
            'transaction_id': transaction_id
        }
        
        subscription = await create_user_subscription(self.db, subscription_data)
        
        try:
            # Создаем пользователя в Marzban
            await self._create_marzban_user(user, subscription, plan)
            
            # Активируем подписку
            subscription.status = SubscriptionStatus.ACTIVE
            await self.db.commit()
            
            logger.info(
                "Подписка успешно создана",
                subscription_id=subscription.id,
                user_id=user.id,
                plan_id=plan.id
            )
            
            return subscription
            
        except Exception as e:
            # Откатываем изменения при ошибке
            await self.db.rollback()
            logger.error(
                "Ошибка создания подписки",
                subscription_id=subscription.id,
                error=str(e)
            )
            raise
    
    async def activate_subscription(self, subscription_id: int) -> UserSubscription:
        """
        Активирует подписку
        
        Args:
            subscription_id: ID подписки
            
        Returns:
            Активированная подписка
        """
        logger.info("Активация подписки", subscription_id=subscription_id)
        
        subscription = await self._get_subscription_by_id(subscription_id)
        if not subscription:
            raise ValueError(f"Подписка {subscription_id} не найдена")
            
        if subscription.status == SubscriptionStatus.ACTIVE:
            logger.warning("Подписка уже активна", subscription_id=subscription_id)
            return subscription
        
        # Получаем связанные данные
        user = subscription.user
        plan = subscription.plan
        
        try:
            # Активируем пользователя в Marzban
            await self._activate_marzban_user(user, subscription)
            
            # Обновляем статус подписки
            subscription.status = SubscriptionStatus.ACTIVE
            subscription.start_date = datetime.now(timezone.utc)
            subscription.end_date = datetime.now(timezone.utc) + timedelta(days=plan.duration_days)
            
            await self.db.commit()
            
            logger.info("Подписка активирована", subscription_id=subscription_id)
            return subscription
            
        except Exception as e:
            await self.db.rollback()
            logger.error(
                "Ошибка активации подписки",
                subscription_id=subscription_id,
                error=str(e)
            )
            raise
    
    async def deactivate_subscription(self, subscription_id: int, reason: str = None) -> UserSubscription:
        """
        Деактивирует подписку
        
        Args:
            subscription_id: ID подписки
            reason: Причина деактивации
            
        Returns:
            Деактивированная подписка
        """
        logger.info(
            "Деактивация подписки",
            subscription_id=subscription_id,
            reason=reason
        )
        
        subscription = await self._get_subscription_by_id(subscription_id)
        if not subscription:
            raise ValueError(f"Подписка {subscription_id} не найдена")
            
        if subscription.status in [SubscriptionStatus.EXPIRED, SubscriptionStatus.CANCELLED]:
            logger.warning("Подписка уже неактивна", subscription_id=subscription_id)
            return subscription
        
        user = subscription.user
        
        try:
            # Деактивируем пользователя в Marzban
            await self._deactivate_marzban_user(user)
            
            # Обновляем статус подписки
            subscription.status = SubscriptionStatus.CANCELLED
            subscription.notes = f"Деактивирована: {reason}" if reason else "Деактивирована"
            
            await self.db.commit()
            
            logger.info("Подписка деактивирована", subscription_id=subscription_id)
            return subscription
            
        except Exception as e:
            await self.db.rollback()
            logger.error(
                "Ошибка деактивации подписки",
                subscription_id=subscription_id,
                error=str(e)
            )
            raise
    
    async def extend_subscription(
        self, 
        subscription_id: int, 
        additional_days: int,
        additional_traffic: Optional[int] = None
    ) -> UserSubscription:
        """
        Продлевает подписку
        
        Args:
            subscription_id: ID подписки
            additional_days: Дополнительные дни
            additional_traffic: Дополнительный трафик в байтах
            
        Returns:
            Продленная подписка
        """
        logger.info(
            "Продление подписки",
            subscription_id=subscription_id,
            additional_days=additional_days,
            additional_traffic=additional_traffic
        )
        
        subscription = await self._get_subscription_by_id(subscription_id)
        if not subscription:
            raise ValueError(f"Подписка {subscription_id} не найдена")
        
        user = subscription.user
        
        try:
            # Продлеваем подписку
            subscription.end_date += timedelta(days=additional_days)
            
            if additional_traffic:
                subscription.traffic_limit += additional_traffic
            
            # Если подписка истекла, активируем её снова
            if subscription.status == SubscriptionStatus.EXPIRED:
                subscription.status = SubscriptionStatus.ACTIVE
                await self._activate_marzban_user(user, subscription)
            else:
                # Обновляем данные в Marzban
                await self._update_marzban_user(user, subscription)
            
            await self.db.commit()
            
            logger.info("Подписка продлена", subscription_id=subscription_id)
            return subscription
            
        except Exception as e:
            await self.db.rollback()
            logger.error(
                "Ошибка продления подписки",
                subscription_id=subscription_id,
                error=str(e)
            )
            raise
    
    async def sync_traffic_usage(self, subscription_id: int) -> UserSubscription:
        """
        Синхронизирует использование трафика с Marzban
        
        Args:
            subscription_id: ID подписки
            
        Returns:
            Обновленная подписка
        """
        subscription = await self._get_subscription_by_id(subscription_id)
        if not subscription:
            raise ValueError(f"Подписка {subscription_id} не найдена")
        
        if subscription.status != SubscriptionStatus.ACTIVE:
            return subscription
        
        user = subscription.user
        marzban_username = create_marzban_username(user.telegram_id, user.username)
        
        try:
            async with self.marzban_client as client:
                # Получаем данные пользователя из Marzban
                marzban_user = await client.get_user(marzban_username)
                
                # Обновляем использование трафика
                subscription.traffic_used = marzban_user.used_traffic
                
                # Проверяем превышение лимитов
                if subscription.traffic_limit and subscription.traffic_used >= subscription.traffic_limit:
                    logger.warning(
                        "Превышен лимит трафика",
                        subscription_id=subscription_id,
                        used=subscription.traffic_used,
                        limit=subscription.traffic_limit
                    )
                    
                    # Деактивируем подписку при превышении лимита
                    await self.deactivate_subscription(subscription_id, "Превышен лимит трафика")
                
                await self.db.commit()
                
                logger.debug(
                    "Трафик синхронизирован",
                    subscription_id=subscription_id,
                    used_traffic=subscription.traffic_used
                )
                
                return subscription
                
        except MarzbanUserNotFoundError:
            logger.warning(
                "Пользователь не найден в Marzban",
                subscription_id=subscription_id,
                marzban_username=marzban_username
            )
            return subscription
        except Exception as e:
            logger.error(
                "Ошибка синхронизации трафика",
                subscription_id=subscription_id,
                error=str(e)
            )
            raise

    async def check_expiring_subscriptions(self, days_before: int = 3) -> List[UserSubscription]:
        """
        Находит подписки, которые истекают в ближайшие дни

        Args:
            days_before: За сколько дней до истечения искать

        Returns:
            Список истекающих подписок
        """
        expiry_threshold = datetime.now(timezone.utc) + timedelta(days=days_before)

        stmt = select(UserSubscription).where(
            and_(
                UserSubscription.status == SubscriptionStatus.ACTIVE,
                UserSubscription.end_date <= expiry_threshold,
                UserSubscription.end_date > datetime.now(timezone.utc)
            )
        )

        result = await self.db.execute(stmt)
        expiring_subscriptions = result.scalars().all()

        logger.info(
            "Найдены истекающие подписки",
            count=len(expiring_subscriptions),
            days_before=days_before
        )

        return expiring_subscriptions

    async def process_expired_subscriptions(self) -> List[UserSubscription]:
        """
        Обрабатывает истекшие подписки

        Returns:
            Список обработанных подписок
        """
        now = datetime.now(timezone.utc)

        stmt = select(UserSubscription).where(
            and_(
                UserSubscription.status == SubscriptionStatus.ACTIVE,
                UserSubscription.end_date <= now
            )
        )

        result = await self.db.execute(stmt)
        expired_subscriptions = result.scalars().all()

        processed_subscriptions = []

        for subscription in expired_subscriptions:
            try:
                logger.info(
                    "Обработка истекшей подписки",
                    subscription_id=subscription.id,
                    end_date=subscription.end_date
                )

                # Деактивируем пользователя в Marzban
                await self._deactivate_marzban_user(subscription.user)

                # Обновляем статус подписки
                subscription.status = SubscriptionStatus.EXPIRED
                subscription.notes = f"Истекла {now.isoformat()}"

                processed_subscriptions.append(subscription)

            except Exception as e:
                logger.error(
                    "Ошибка обработки истекшей подписки",
                    subscription_id=subscription.id,
                    error=str(e)
                )

        if processed_subscriptions:
            await self.db.commit()
            logger.info(
                "Обработаны истекшие подписки",
                count=len(processed_subscriptions)
            )

        return processed_subscriptions

    async def get_user_active_subscription(self, user_telegram_id: int) -> Optional[UserSubscription]:
        """
        Получает активную подписку пользователя

        Args:
            user_telegram_id: Telegram ID пользователя

        Returns:
            Активная подписка или None
        """
        user = await get_user_by_telegram_id(self.db, user_telegram_id)
        if not user:
            return None

        subscriptions = await self._get_active_subscriptions(user.id)
        return subscriptions[0] if subscriptions else None

    async def get_subscription_info(self, subscription_id: int) -> Dict[str, Any]:
        """
        Получает подробную информацию о подписке

        Args:
            subscription_id: ID подписки

        Returns:
            Словарь с информацией о подписке
        """
        subscription = await self._get_subscription_by_id(subscription_id)
        if not subscription:
            raise ValueError(f"Подписка {subscription_id} не найдена")

        # Синхронизируем трафик
        await self.sync_traffic_usage(subscription_id)

        # Формируем информацию
        info = {
            'id': subscription.id,
            'status': subscription.status.value,
            'plan_name': subscription.plan.name,
            'start_date': subscription.start_date,
            'end_date': subscription.end_date,
            'days_remaining': (subscription.end_date - datetime.now(timezone.utc)).days,
            'traffic_limit': subscription.traffic_limit,
            'traffic_used': subscription.traffic_used,
            'traffic_remaining': subscription.traffic_limit - subscription.traffic_used if subscription.traffic_limit else None,
            'traffic_usage_percent': (subscription.traffic_used / subscription.traffic_limit * 100) if subscription.traffic_limit else 0,
            'is_expired': subscription.end_date <= datetime.now(timezone.utc),
            'is_quota_exceeded': subscription.traffic_limit and subscription.traffic_used >= subscription.traffic_limit,
            'notes': subscription.notes
        }

        return info

    # Вспомогательные методы

    async def _get_subscription_by_id(self, subscription_id: int) -> Optional[UserSubscription]:
        """Получает подписку по ID с загрузкой связанных данных"""
        stmt = select(UserSubscription).where(UserSubscription.id == subscription_id)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    async def _get_active_subscriptions(self, user_id: int) -> List[UserSubscription]:
        """Получает активные подписки пользователя"""
        stmt = select(UserSubscription).where(
            and_(
                UserSubscription.user_id == user_id,
                UserSubscription.status == SubscriptionStatus.ACTIVE
            )
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def _create_marzban_user(
        self,
        user: User,
        subscription: UserSubscription,
        plan: SubscriptionPlan
    ) -> None:
        """Создает пользователя в Marzban"""
        marzban_username = create_marzban_username(user.telegram_id, user.username)

        # Вычисляем дату истечения в timestamp
        expire_timestamp = int(subscription.end_date.timestamp()) if subscription.end_date else None

        user_data = MarzbanUserCreate(
            username=marzban_username,
            proxies=plan.marzban_config or {
                "vmess": {"id": f"user-{user.telegram_id}"},
                "vless": {"id": f"user-{user.telegram_id}"}
            },
            data_limit=subscription.traffic_limit,
            expire=expire_timestamp,
            status=MarzbanUserStatus.ACTIVE,
            note=f"Telegram: @{user.username or user.telegram_id}, Plan: {plan.name}"
        )

        async with self.marzban_client as client:
            try:
                await client.create_user(user_data)
                logger.info(
                    "Пользователь создан в Marzban",
                    marzban_username=marzban_username,
                    user_id=user.id
                )
            except MarzbanUserAlreadyExistsError:
                logger.warning(
                    "Пользователь уже существует в Marzban",
                    marzban_username=marzban_username
                )
                # Обновляем существующего пользователя
                await self._update_marzban_user(user, subscription)

    async def _activate_marzban_user(
        self,
        user: User,
        subscription: UserSubscription
    ) -> None:
        """Активирует пользователя в Marzban"""
        marzban_username = create_marzban_username(user.telegram_id, user.username)

        expire_timestamp = int(subscription.end_date.timestamp()) if subscription.end_date else None

        update_data = MarzbanUserUpdate(
            data_limit=subscription.traffic_limit,
            expire=expire_timestamp,
            status=MarzbanUserStatus.ACTIVE
        )

        async with self.marzban_client as client:
            await client.update_user(marzban_username, update_data)
            logger.info(
                "Пользователь активирован в Marzban",
                marzban_username=marzban_username
            )

    async def _deactivate_marzban_user(self, user: User) -> None:
        """Деактивирует пользователя в Marzban"""
        marzban_username = create_marzban_username(user.telegram_id, user.username)

        update_data = MarzbanUserUpdate(status=MarzbanUserStatus.DISABLED)

        async with self.marzban_client as client:
            try:
                await client.update_user(marzban_username, update_data)
                logger.info(
                    "Пользователь деактивирован в Marzban",
                    marzban_username=marzban_username
                )
            except MarzbanUserNotFoundError:
                logger.warning(
                    "Пользователь не найден в Marzban при деактивации",
                    marzban_username=marzban_username
                )

    async def _update_marzban_user(self, user: User, subscription: UserSubscription) -> None:
        """Обновляет данные пользователя в Marzban"""
        marzban_username = create_marzban_username(user.telegram_id, user.username)

        expire_timestamp = int(subscription.end_date.timestamp()) if subscription.end_date else None

        update_data = MarzbanUserUpdate(
            data_limit=subscription.traffic_limit,
            expire=expire_timestamp,
            status=MarzbanUserStatus.ACTIVE if subscription.status == SubscriptionStatus.ACTIVE else MarzbanUserStatus.DISABLED
        )

        async with self.marzban_client as client:
            await client.update_user(marzban_username, update_data)
            logger.info(
                "Данные пользователя обновлены в Marzban",
                marzban_username=marzban_username
            )
