"""
Сервис уведомлений

Обеспечивает отправку уведомлений пользователям о состоянии подписок,
истечении сроков действия, превышении лимитов трафика и других событиях.
"""

import asyncio
from datetime import datetime, timezone
from typing import List, Optional, Dict
from enum import Enum
import structlog
from aiogram import Bot
from aiogram.exceptions import TelegramBadRequest, TelegramForbiddenError

from app.database.models import User, UserSubscription
from app.utils.helpers import format_bytes, format_datetime

logger = structlog.get_logger()


class NotificationType(str, Enum):
    """Типы уведомлений"""
    SUBSCRIPTION_CREATED = "subscription_created"
    SUBSCRIPTION_ACTIVATED = "subscription_activated"
    SUBSCRIPTION_EXPIRED = "subscription_expired"
    SUBSCRIPTION_EXPIRING = "subscription_expiring"
    TRAFFIC_LIMIT_WARNING = "traffic_limit_warning"
    TRAFFIC_LIMIT_EXCEEDED = "traffic_limit_exceeded"
    PAYMENT_RECEIVED = "payment_received"
    PAYMENT_FAILED = "payment_failed"


class NotificationService:
    """
    Сервис уведомлений
    
    Следует принципам A2A:
    - Async First: Асинхронная отправка уведомлений
    - Enterprise Ready: Обработка ошибок, логирование, повторные попытки
    - Opaque Execution: Инкапсуляция логики уведомлений
    """
    
    def __init__(self, bot: Optional[Bot] = None):
        """
        Инициализация сервиса уведомлений
        
        Args:
            bot: Экземпляр Telegram бота
        """
        self.bot = bot
        
    async def send_subscription_created(self, user: User, subscription: UserSubscription) -> bool:
        """
        Отправляет уведомление о создании подписки
        
        Args:
            user: Пользователь
            subscription: Подписка
            
        Returns:
            True если уведомление отправлено успешно
        """
        message = self._format_subscription_created_message(subscription)
        return await self._send_notification(
            user.telegram_id,
            message,
            NotificationType.SUBSCRIPTION_CREATED
        )
    
    async def send_subscription_activated(self, user: User, subscription: UserSubscription) -> bool:
        """
        Отправляет уведомление об активации подписки
        
        Args:
            user: Пользователь
            subscription: Подписка
            
        Returns:
            True если уведомление отправлено успешно
        """
        message = self._format_subscription_activated_message(subscription)
        return await self._send_notification(
            user.telegram_id,
            message,
            NotificationType.SUBSCRIPTION_ACTIVATED
        )

    async def send_subscription_expiring(self, user: User, subscription: UserSubscription, days_left: int) -> bool:
        """
        Отправляет уведомление о скором истечении подписки

        Args:
            user: Пользователь
            subscription: Подписка
            days_left: Дней до истечения

        Returns:
            True если уведомление отправлено успешно
        """
        message = self._format_subscription_expiring_message(subscription, days_left)
        return await self._send_notification(
            user.telegram_id,
            message,
            NotificationType.SUBSCRIPTION_EXPIRING
        )
    
    async def send_subscription_expired(self, user: User, subscription: UserSubscription) -> bool:
        """
        Отправляет уведомление об истечении подписки
        
        Args:
            user: Пользователь
            subscription: Подписка
            
        Returns:
            True если уведомление отправлено успешно
        """
        message = self._format_subscription_expired_message(subscription)
        return await self._send_notification(
            user.telegram_id,
            message,
            NotificationType.SUBSCRIPTION_EXPIRED
        )

    async def send_traffic_warning(self, user: User, subscription: UserSubscription, usage_percent: float) -> bool:
        """
        Отправляет предупреждение о превышении лимита трафика

        Args:
            user: Пользователь
            subscription: Подписка
            usage_percent: Процент использования трафика

        Returns:
            True если уведомление отправлено успешно
        """
        message = self._format_traffic_warning_message(subscription, usage_percent)
        return await self._send_notification(
            user.telegram_id,
            message,
            NotificationType.TRAFFIC_LIMIT_WARNING
        )
    
    async def send_traffic_exceeded(self, user: User, subscription: UserSubscription) -> bool:
        """
        Отправляет уведомление о превышении лимита трафика
        
        Args:
            user: Пользователь
            subscription: Подписка
            
        Returns:
            True если уведомление отправлено успешно
        """
        message = self._format_traffic_exceeded_message(subscription)
        return await self._send_notification(
            user.telegram_id,
            message,
            NotificationType.TRAFFIC_LIMIT_EXCEEDED
        )
    
    async def send_payment_received(self, user: User, amount: float, currency: str = "RUB") -> bool:
        """
        Отправляет уведомление о получении платежа
        
        Args:
            user: Пользователь
            amount: Сумма платежа
            currency: Валюта
            
        Returns:
            True если уведомление отправлено успешно
        """
        message = self._format_payment_received_message(user, amount, currency)
        return await self._send_notification(
            user.telegram_id,
            message,
            NotificationType.PAYMENT_RECEIVED
        )
    
    async def send_bulk_notifications(
        self, 
        users_and_messages: List[tuple[User, str, NotificationType]]
    ) -> Dict[str, int]:
        """
        Отправляет массовые уведомления
        
        Args:
            users_and_messages: Список кортежей (пользователь, сообщение, тип)
            
        Returns:
            Статистика отправки
        """
        stats = {
            "sent": 0,
            "failed": 0,
            "blocked": 0,
            "errors": 0
        }
        
        tasks = []
        for user, message, notification_type in users_and_messages:
            task = self._send_notification_with_stats(user.telegram_id, message, notification_type, stats)
            tasks.append(task)
        
        # Отправляем уведомления параллельно с ограничением
        semaphore = asyncio.Semaphore(10)  # Максимум 10 одновременных отправок
        
        async def limited_send(task):
            async with semaphore:
                return await task
        
        await asyncio.gather(*[limited_send(task) for task in tasks], return_exceptions=True)
        
        logger.info(
            "Массовая отправка уведомлений завершена",
            total=len(users_and_messages),
            **stats
        )
        
        return stats
    
    # Приватные методы для форматирования сообщений
    
    def _format_subscription_created_message(self, subscription: UserSubscription) -> str:
        """Форматирует сообщение о создании подписки"""
        plan_name = subscription.plan.name
        end_date = format_datetime(subscription.end_date)
        traffic_limit = format_bytes(subscription.traffic_limit) if subscription.traffic_limit else "Безлимитно"
        
        return f"""🎉 <b>Подписка создана!</b>

📋 <b>План:</b> {plan_name}
📅 <b>Действует до:</b> {end_date}
📊 <b>Лимит трафика:</b> {traffic_limit}

Ваша подписка будет активирована после подтверждения оплаты.

Для получения конфигурации используйте команду /config"""
    
    def _format_subscription_activated_message(self, subscription: UserSubscription) -> str:
        """Форматирует сообщение об активации подписки"""
        plan_name = subscription.plan.name
        end_date = format_datetime(subscription.end_date)
        traffic_limit = format_bytes(subscription.traffic_limit) if subscription.traffic_limit else "Безлимитно"

        return f"""✅ <b>Подписка активирована!</b>

📋 <b>План:</b> {plan_name}
📅 <b>Действует до:</b> {end_date}
📊 <b>Лимит трафика:</b> {traffic_limit}

🔗 Получить конфигурацию: /config
📊 Проверить статус: /status"""

    def _format_subscription_expiring_message(self, subscription: UserSubscription, days_left: int) -> str:
        """Форматирует сообщение о скором истечении подписки"""
        plan_name = subscription.plan.name
        end_date = format_datetime(subscription.end_date)
        
        days_text = "день" if days_left == 1 else "дня" if days_left < 5 else "дней"
        
        return f"""⚠️ <b>Подписка скоро истечет!</b>

📋 <b>План:</b> {plan_name}
📅 <b>Истекает:</b> {end_date}
⏰ <b>Осталось:</b> {days_left} {days_text}

Продлите подписку, чтобы не потерять доступ к VPN.

💳 Продлить подписку: /renew"""
    
    def _format_subscription_expired_message(self, subscription: UserSubscription) -> str:
        """Форматирует сообщение об истечении подписки"""
        plan_name = subscription.plan.name
        end_date = format_datetime(subscription.end_date)
        
        return f"""❌ <b>Подписка истекла</b>

📋 <b>План:</b> {plan_name}
📅 <b>Истекла:</b> {end_date}

Доступ к VPN приостановлен. Продлите подписку для восстановления доступа.

💳 Продлить подписку: /renew
🛒 Выбрать новый план: /plans"""
    
    def _format_traffic_warning_message(self, subscription: UserSubscription, usage_percent: float) -> str:
        """Форматирует предупреждение о трафике"""
        used = format_bytes(subscription.traffic_used)
        limit = format_bytes(subscription.traffic_limit) if subscription.traffic_limit else "∞"
        
        return f"""⚠️ <b>Предупреждение о трафике</b>

📊 <b>Использовано:</b> {used} из {limit} ({usage_percent:.1f}%)

При превышении лимита доступ к VPN будет приостановлен.

📊 Проверить статус: /status
💳 Увеличить лимит: /upgrade"""
    
    def _format_traffic_exceeded_message(self, subscription: UserSubscription) -> str:
        """Форматирует сообщение о превышении лимита трафика"""
        used = format_bytes(subscription.traffic_used)
        limit = format_bytes(subscription.traffic_limit) if subscription.traffic_limit else "∞"
        
        return f"""🚫 <b>Лимит трафика превышен</b>

📊 <b>Использовано:</b> {used} из {limit}

Доступ к VPN приостановлен до увеличения лимита или продления подписки.

💳 Увеличить лимит: /upgrade
🔄 Продлить подписку: /renew"""
    
    def _format_payment_received_message(self, user: User, amount: float, currency: str) -> str:
        """Форматирует сообщение о получении платежа"""
        return f"""💰 <b>Платеж получен!</b>

💵 <b>Сумма:</b> {amount:.2f} {currency}
⏰ <b>Время:</b> {format_datetime(datetime.now(timezone.utc))}

Ваша подписка будет активирована в течение нескольких минут.

📊 Проверить статус: /status"""
    
    async def _send_notification(
        self, 
        telegram_id: int, 
        message: str, 
        notification_type: NotificationType
    ) -> bool:
        """
        Отправляет уведомление пользователю
        
        Args:
            telegram_id: Telegram ID пользователя
            message: Текст сообщения
            notification_type: Тип уведомления
            
        Returns:
            True если отправлено успешно
        """
        if not self.bot:
            logger.warning("Bot не инициализирован, уведомление не отправлено")
            return False
        
        try:
            await self.bot.send_message(
                chat_id=telegram_id,
                text=message,
                parse_mode="HTML"
            )
            
            logger.info(
                "Уведомление отправлено",
                telegram_id=telegram_id,
                notification_type=notification_type.value
            )
            return True
            
        except TelegramForbiddenError:
            logger.warning(
                "Пользователь заблокировал бота",
                telegram_id=telegram_id,
                notification_type=notification_type.value
            )
            # Пробрасываем исключение для корректной обработки в bulk методах
            raise
            
        except TelegramBadRequest as e:
            logger.warning(
                "Ошибка отправки уведомления",
                telegram_id=telegram_id,
                notification_type=notification_type.value,
                error=str(e)
            )
            return False
            
        except Exception as e:
            logger.error(
                "Неожиданная ошибка отправки уведомления",
                telegram_id=telegram_id,
                notification_type=notification_type.value,
                error=str(e)
            )
            return False
    
    async def _send_notification_with_stats(
        self,
        telegram_id: int,
        message: str,
        notification_type: NotificationType,
        stats: Dict[str, int]
    ) -> None:
        """Отправляет уведомление с обновлением статистики"""
        try:
            success = await self._send_notification(telegram_id, message, notification_type)
            if success:
                stats["sent"] += 1
            else:
                stats["failed"] += 1
        except TelegramForbiddenError:
            stats["blocked"] += 1
        except Exception:
            stats["errors"] += 1
