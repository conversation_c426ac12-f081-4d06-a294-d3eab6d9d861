"""
Провайдер для интеграции с ЮKassa API
"""

import aiohttp
import json
import hmac
import hashlib
import base64
from typing import Dict, Any, Optional
from decimal import Decimal
import structlog
from datetime import datetime, timezone
import uuid

from app.services.payment_service import (
    PaymentProviderInterface,
    PaymentResult,
    PaymentStatus,
    PaymentProvider
)
from app.core.config import settings

logger = structlog.get_logger()


class YooKassaProvider(PaymentProviderInterface):
    """Провайдер для работы с ЮKassa API"""
    
    def __init__(self, shop_id: str, secret_key: str):
        self.shop_id = shop_id
        self.secret_key = secret_key
        self.base_url = "https://api.yookassa.ru/v3"
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Получает HTTP сессию"""
        if self.session is None or self.session.closed:
            auth = aiohttp.BasicAuth(self.shop_id, self.secret_key)
            headers = {
                "Content-Type": "application/json",
                "User-Agent": "Unveil-VPN/1.0"
            }
            self.session = aiohttp.ClientSession(
                auth=auth,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=30)
            )
        return self.session
    
    async def close(self):
        """Закрывает HTTP сессию"""
        if self.session and not self.session.closed:
            await self.session.close()
    
    async def create_payment(
        self,
        amount: Decimal,
        currency: str,
        description: str,
        return_url: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> PaymentResult:
        """
        Создает платеж в ЮKassa
        
        Args:
            amount: Сумма платежа
            currency: Валюта (RUB)
            description: Описание платежа
            return_url: URL для возврата
            metadata: Дополнительные данные
            
        Returns:
            PaymentResult: Результат создания платежа
        """
        session = await self._get_session()
        
        # Генерируем уникальный ключ идемпотентности
        idempotence_key = str(uuid.uuid4())
        
        # Подготавливаем данные платежа
        payment_data = {
            "amount": {
                "value": str(amount),
                "currency": currency.upper()
            },
            "description": description,
            "confirmation": {
                "type": "redirect",
                "return_url": return_url or settings.YOOKASSA_RETURN_URL
            },
            "capture": True,  # Автоматическое списание
            "metadata": metadata or {}
        }
        
        headers = {
            "Idempotence-Key": idempotence_key
        }
        
        try:
            async with session.post(
                f"{self.base_url}/payments",
                json=payment_data,
                headers=headers
            ) as response:
                response_data = await response.json()
                
                if response.status == 200:
                    payment_id = response_data["id"]
                    status = self._map_yookassa_status(response_data["status"])
                    confirmation_url = response_data.get("confirmation", {}).get("confirmation_url")
                    
                    logger.info(
                        "Создан платеж ЮKassa",
                        payment_id=payment_id,
                        amount=float(amount),
                        currency=currency,
                        status=status.value
                    )
                    
                    return PaymentResult(
                        payment_id=payment_id,
                        status=status,
                        payment_url=confirmation_url,
                        amount=amount,
                        currency=currency,
                        provider=PaymentProvider.YOOKASSA,
                        metadata=response_data.get("metadata", {})
                    )
                else:
                    error_msg = response_data.get("description", "Неизвестная ошибка")
                    logger.error(
                        "Ошибка создания платежа ЮKassa",
                        status=response.status,
                        error=error_msg,
                        response=response_data
                    )
                    raise Exception(f"Ошибка ЮKassa: {error_msg}")
                    
        except aiohttp.ClientError as e:
            logger.error("Ошибка соединения с ЮKassa", error=str(e))
            raise Exception(f"Ошибка соединения с ЮKassa: {str(e)}")
    
    async def get_payment_status(self, payment_id: str) -> PaymentResult:
        """
        Получает статус платежа из ЮKassa
        
        Args:
            payment_id: ID платежа
            
        Returns:
            PaymentResult: Статус платежа
        """
        session = await self._get_session()
        
        try:
            async with session.get(f"{self.base_url}/payments/{payment_id}") as response:
                response_data = await response.json()
                
                if response.status == 200:
                    status = self._map_yookassa_status(response_data["status"])
                    amount = Decimal(response_data["amount"]["value"])
                    currency = response_data["amount"]["currency"]
                    
                    return PaymentResult(
                        payment_id=payment_id,
                        status=status,
                        amount=amount,
                        currency=currency,
                        provider=PaymentProvider.YOOKASSA,
                        metadata=response_data.get("metadata", {})
                    )
                else:
                    error_msg = response_data.get("description", "Платеж не найден")
                    logger.error(
                        "Ошибка получения статуса платежа ЮKassa",
                        payment_id=payment_id,
                        status=response.status,
                        error=error_msg
                    )
                    raise Exception(f"Ошибка ЮKassa: {error_msg}")
                    
        except aiohttp.ClientError as e:
            logger.error("Ошибка соединения с ЮKassa", error=str(e))
            raise Exception(f"Ошибка соединения с ЮKassa: {str(e)}")
    
    async def cancel_payment(self, payment_id: str) -> PaymentResult:
        """
        Отменяет платеж в ЮKassa
        
        Args:
            payment_id: ID платежа
            
        Returns:
            PaymentResult: Результат отмены
        """
        session = await self._get_session()
        
        idempotence_key = str(uuid.uuid4())
        headers = {"Idempotence-Key": idempotence_key}
        
        try:
            async with session.post(
                f"{self.base_url}/payments/{payment_id}/cancel",
                headers=headers
            ) as response:
                response_data = await response.json()
                
                if response.status == 200:
                    status = self._map_yookassa_status(response_data["status"])
                    amount = Decimal(response_data["amount"]["value"])
                    currency = response_data["amount"]["currency"]
                    
                    logger.info("Отменен платеж ЮKassa", payment_id=payment_id)
                    
                    return PaymentResult(
                        payment_id=payment_id,
                        status=status,
                        amount=amount,
                        currency=currency,
                        provider=PaymentProvider.YOOKASSA,
                        metadata=response_data.get("metadata", {})
                    )
                else:
                    error_msg = response_data.get("description", "Ошибка отмены")
                    logger.error(
                        "Ошибка отмены платежа ЮKassa",
                        payment_id=payment_id,
                        error=error_msg
                    )
                    raise Exception(f"Ошибка ЮKassa: {error_msg}")
                    
        except aiohttp.ClientError as e:
            logger.error("Ошибка соединения с ЮKassa", error=str(e))
            raise Exception(f"Ошибка соединения с ЮKassa: {str(e)}")
    
    async def refund_payment(
        self,
        payment_id: str,
        amount: Optional[Decimal] = None,
        reason: Optional[str] = None
    ) -> PaymentResult:
        """
        Возвращает платеж в ЮKassa
        
        Args:
            payment_id: ID платежа
            amount: Сумма возврата
            reason: Причина возврата
            
        Returns:
            PaymentResult: Результат возврата
        """
        session = await self._get_session()
        
        # Сначала получаем информацию о платеже
        payment_info = await self.get_payment_status(payment_id)
        
        refund_data = {
            "payment_id": payment_id
        }
        
        if amount:
            refund_data["amount"] = {
                "value": str(amount),
                "currency": payment_info.currency
            }
        
        if reason:
            refund_data["description"] = reason
        
        idempotence_key = str(uuid.uuid4())
        headers = {"Idempotence-Key": idempotence_key}
        
        try:
            async with session.post(
                f"{self.base_url}/refunds",
                json=refund_data,
                headers=headers
            ) as response:
                response_data = await response.json()
                
                if response.status == 200:
                    refund_amount = Decimal(response_data["amount"]["value"])
                    
                    logger.info(
                        "Создан возврат ЮKassa",
                        payment_id=payment_id,
                        refund_id=response_data["id"],
                        amount=float(refund_amount)
                    )
                    
                    return PaymentResult(
                        payment_id=response_data["id"],  # ID возврата
                        status=PaymentStatus.SUCCEEDED,
                        amount=refund_amount,
                        currency=response_data["amount"]["currency"],
                        provider=PaymentProvider.YOOKASSA,
                        metadata={"original_payment_id": payment_id}
                    )
                else:
                    error_msg = response_data.get("description", "Ошибка возврата")
                    logger.error(
                        "Ошибка возврата платежа ЮKassa",
                        payment_id=payment_id,
                        error=error_msg
                    )
                    raise Exception(f"Ошибка ЮKassa: {error_msg}")
                    
        except aiohttp.ClientError as e:
            logger.error("Ошибка соединения с ЮKassa", error=str(e))
            raise Exception(f"Ошибка соединения с ЮKassa: {str(e)}")
    
    def validate_webhook(self, headers: Dict[str, str], body: bytes) -> bool:
        """
        Валидирует webhook от ЮKassa
        
        Args:
            headers: HTTP заголовки
            body: Тело запроса
            
        Returns:
            bool: True если webhook валидный
        """
        # ЮKassa не использует подпись webhook'ов
        # Валидация происходит через HTTPS и IP адреса
        return True
    
    async def process_webhook(
        self,
        headers: Dict[str, str],
        body: Dict[str, Any]
    ) -> Optional[PaymentResult]:
        """
        Обрабатывает webhook от ЮKassa
        
        Args:
            headers: HTTP заголовки
            body: Тело запроса
            
        Returns:
            PaymentResult: Обновленный статус платежа
        """
        try:
            event_type = body.get("event")
            payment_object = body.get("object")
            
            if not payment_object:
                logger.warning("Webhook ЮKassa без объекта платежа")
                return None
            
            payment_id = payment_object.get("id")
            if not payment_id:
                logger.warning("Webhook ЮKassa без ID платежа")
                return None
            
            status = self._map_yookassa_status(payment_object.get("status"))
            amount = Decimal(payment_object["amount"]["value"])
            currency = payment_object["amount"]["currency"]
            
            logger.info(
                "Получен webhook ЮKassa",
                event=event_type,
                payment_id=payment_id,
                status=status.value
            )
            
            return PaymentResult(
                payment_id=payment_id,
                status=status,
                amount=amount,
                currency=currency,
                provider=PaymentProvider.YOOKASSA,
                metadata=payment_object.get("metadata", {})
            )
            
        except Exception as e:
            logger.error("Ошибка обработки webhook ЮKassa", error=str(e))
            return None
    
    def _map_yookassa_status(self, yookassa_status: str) -> PaymentStatus:
        """Маппинг статусов ЮKassa в внутренние статусы"""
        mapping = {
            "pending": PaymentStatus.PENDING,
            "waiting_for_capture": PaymentStatus.WAITING_FOR_CAPTURE,
            "succeeded": PaymentStatus.SUCCEEDED,
            "canceled": PaymentStatus.CANCELED,
            "failed": PaymentStatus.FAILED
        }
        return mapping.get(yookassa_status, PaymentStatus.PENDING)
