"""
Провайдер для интеграции с Cryptomus API
"""

import aiohttp
import json
import hmac
import hashlib
import base64
from typing import Dict, Any, Optional
from decimal import Decimal
import structlog
from datetime import datetime, timezone
import uuid

from app.services.payment_service import (
    PaymentProviderInterface,
    PaymentResult,
    PaymentStatus,
    PaymentProvider
)
from app.core.config import settings

logger = structlog.get_logger()


class CryptomusProvider(PaymentProviderInterface):
    """Провайдер для работы с Cryptomus API"""
    
    def __init__(self, merchant_id: str, api_key: str):
        self.merchant_id = merchant_id
        self.api_key = api_key
        self.base_url = "https://api.cryptomus.com/v1"
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Получает HTTP сессию"""
        if self.session is None or self.session.closed:
            headers = {
                "Content-Type": "application/json",
                "User-Agent": "Unveil-VPN/1.0"
            }
            self.session = aiohttp.ClientSession(
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=30)
            )
        return self.session
    
    async def close(self):
        """Закрывает HTTP сессию"""
        if self.session and not self.session.closed:
            await self.session.close()
    
    def _generate_signature(self, data: Dict[str, Any]) -> str:
        """
        Генерирует подпись для запроса к Cryptomus API
        
        Args:
            data: Данные запроса
            
        Returns:
            str: Подпись запроса
        """
        # Сортируем данные по ключам и создаем строку
        sorted_data = dict(sorted(data.items()))
        data_string = json.dumps(sorted_data, separators=(',', ':'), ensure_ascii=False)
        
        # Создаем подпись HMAC-SHA256
        signature = hmac.new(
            self.api_key.encode('utf-8'),
            data_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return signature
    
    async def create_payment(
        self,
        amount: Decimal,
        currency: str,
        description: str,
        return_url: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> PaymentResult:
        """
        Создает платеж в Cryptomus
        
        Args:
            amount: Сумма платежа
            currency: Валюта (USDT, BTC, ETH и др.)
            description: Описание платежа
            return_url: URL для возврата
            metadata: Дополнительные данные
            
        Returns:
            PaymentResult: Результат создания платежа
        """
        session = await self._get_session()
        
        # Генерируем уникальный order_id
        order_id = str(uuid.uuid4())
        
        # Подготавливаем данные платежа
        payment_data = {
            "amount": str(amount),
            "currency": currency.upper(),
            "order_id": order_id,
            "url_return": return_url or settings.CRYPTOMUS_RETURN_URL,
            "url_callback": settings.CRYPTOMUS_WEBHOOK_URL,
            "is_payment_multiple": False,
            "lifetime": 3600,  # 1 час
            "to_currency": currency.upper()
        }
        
        # Добавляем метаданные если есть
        if metadata:
            payment_data["additional_data"] = json.dumps(metadata)
        
        # Генерируем подпись
        signature = self._generate_signature(payment_data)
        
        headers = {
            "merchant": self.merchant_id,
            "sign": signature
        }
        
        try:
            async with session.post(
                f"{self.base_url}/payment",
                json=payment_data,
                headers=headers
            ) as response:
                response_data = await response.json()
                
                if response.status == 200 and response_data.get("state") == 0:
                    result_data = response_data.get("result", {})
                    payment_id = result_data.get("uuid")
                    payment_url = result_data.get("url")
                    
                    logger.info(
                        "Создан платеж Cryptomus",
                        payment_id=payment_id,
                        order_id=order_id,
                        amount=float(amount),
                        currency=currency
                    )
                    
                    return PaymentResult(
                        payment_id=payment_id,
                        status=PaymentStatus.PENDING,
                        payment_url=payment_url,
                        amount=amount,
                        currency=currency,
                        provider=PaymentProvider.CRYPTOMUS,
                        metadata={"order_id": order_id}
                    )
                else:
                    error_msg = response_data.get("message", "Неизвестная ошибка")
                    logger.error(
                        "Ошибка создания платежа Cryptomus",
                        status=response.status,
                        error=error_msg,
                        response=response_data
                    )
                    raise Exception(f"Ошибка Cryptomus: {error_msg}")
                    
        except aiohttp.ClientError as e:
            logger.error("Ошибка соединения с Cryptomus", error=str(e))
            raise Exception(f"Ошибка соединения с Cryptomus: {str(e)}")
    
    async def get_payment_status(self, payment_id: str) -> PaymentResult:
        """
        Получает статус платежа из Cryptomus
        
        Args:
            payment_id: ID платежа
            
        Returns:
            PaymentResult: Статус платежа
        """
        session = await self._get_session()
        
        request_data = {
            "uuid": payment_id
        }
        
        signature = self._generate_signature(request_data)
        
        headers = {
            "merchant": self.merchant_id,
            "sign": signature
        }
        
        try:
            async with session.post(
                f"{self.base_url}/payment/info",
                json=request_data,
                headers=headers
            ) as response:
                response_data = await response.json()
                
                if response.status == 200 and response_data.get("state") == 0:
                    result_data = response_data.get("result", {})
                    status = self._map_cryptomus_status(result_data.get("payment_status"))
                    amount = Decimal(result_data.get("amount", "0"))
                    currency = result_data.get("currency", "")
                    
                    return PaymentResult(
                        payment_id=payment_id,
                        status=status,
                        amount=amount,
                        currency=currency,
                        provider=PaymentProvider.CRYPTOMUS,
                        metadata=result_data
                    )
                else:
                    error_msg = response_data.get("message", "Платеж не найден")
                    logger.error(
                        "Ошибка получения статуса платежа Cryptomus",
                        payment_id=payment_id,
                        error=error_msg
                    )
                    raise Exception(f"Ошибка Cryptomus: {error_msg}")
                    
        except aiohttp.ClientError as e:
            logger.error("Ошибка соединения с Cryptomus", error=str(e))
            raise Exception(f"Ошибка соединения с Cryptomus: {str(e)}")
    
    async def cancel_payment(self, payment_id: str) -> PaymentResult:
        """
        Отменяет платеж в Cryptomus
        
        Args:
            payment_id: ID платежа
            
        Returns:
            PaymentResult: Результат отмены
        """
        # Cryptomus не поддерживает отмену платежей через API
        # Платежи автоматически истекают через заданное время
        logger.warning("Cryptomus не поддерживает отмену платежей", payment_id=payment_id)
        
        # Получаем текущий статус
        return await self.get_payment_status(payment_id)
    
    async def refund_payment(
        self,
        payment_id: str,
        amount: Optional[Decimal] = None,
        reason: Optional[str] = None
    ) -> PaymentResult:
        """
        Возвращает платеж в Cryptomus
        
        Args:
            payment_id: ID платежа
            amount: Сумма возврата
            reason: Причина возврата
            
        Returns:
            PaymentResult: Результат возврата
        """
        # Cryptomus не поддерживает автоматические возвраты через API
        # Возвраты обрабатываются вручную через панель управления
        logger.warning("Cryptomus не поддерживает автоматические возвраты", payment_id=payment_id)
        raise Exception("Cryptomus не поддерживает автоматические возвраты")
    
    def validate_webhook(self, headers: Dict[str, str], body: bytes) -> bool:
        """
        Валидирует webhook от Cryptomus
        
        Args:
            headers: HTTP заголовки
            body: Тело запроса
            
        Returns:
            bool: True если webhook валидный
        """
        try:
            # Получаем подпись из заголовков
            received_signature = headers.get("sign", "")
            if not received_signature:
                return False
            
            # Парсим тело запроса
            body_data = json.loads(body.decode('utf-8'))
            
            # Генерируем ожидаемую подпись
            expected_signature = self._generate_signature(body_data)
            
            # Сравниваем подписи
            return hmac.compare_digest(received_signature, expected_signature)
            
        except Exception as e:
            logger.error("Ошибка валидации webhook Cryptomus", error=str(e))
            return False
    
    async def process_webhook(
        self,
        headers: Dict[str, str],
        body: Dict[str, Any]
    ) -> Optional[PaymentResult]:
        """
        Обрабатывает webhook от Cryptomus
        
        Args:
            headers: HTTP заголовки
            body: Тело запроса
            
        Returns:
            PaymentResult: Обновленный статус платежа
        """
        try:
            payment_id = body.get("uuid")
            if not payment_id:
                logger.warning("Webhook Cryptomus без ID платежа")
                return None
            
            status = self._map_cryptomus_status(body.get("status"))
            amount = Decimal(body.get("amount", "0"))
            currency = body.get("currency", "")
            
            logger.info(
                "Получен webhook Cryptomus",
                payment_id=payment_id,
                status=status.value,
                amount=float(amount)
            )
            
            return PaymentResult(
                payment_id=payment_id,
                status=status,
                amount=amount,
                currency=currency,
                provider=PaymentProvider.CRYPTOMUS,
                metadata=body
            )
            
        except Exception as e:
            logger.error("Ошибка обработки webhook Cryptomus", error=str(e))
            return None
    
    def _map_cryptomus_status(self, cryptomus_status: str) -> PaymentStatus:
        """Маппинг статусов Cryptomus в внутренние статусы"""
        mapping = {
            "check": PaymentStatus.PENDING,
            "process": PaymentStatus.PENDING,
            "confirm_check": PaymentStatus.PENDING,
            "paid": PaymentStatus.SUCCEEDED,
            "paid_over": PaymentStatus.SUCCEEDED,
            "fail": PaymentStatus.FAILED,
            "cancel": PaymentStatus.CANCELED,
            "system_fail": PaymentStatus.FAILED,
            "refund_process": PaymentStatus.PENDING,
            "refund_fail": PaymentStatus.FAILED,
            "refund_paid": PaymentStatus.SUCCEEDED
        }
        return mapping.get(cryptomus_status, PaymentStatus.PENDING)
