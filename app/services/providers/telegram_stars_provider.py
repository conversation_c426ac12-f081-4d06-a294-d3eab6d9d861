"""
Провайдер для интеграции с Telegram Stars API
"""

from typing import Dict, Any, Optional
from decimal import Decimal
import structlog
from datetime import datetime, timezone
import uuid

from aiogram import Bo<PERSON>
from aiogram.types import LabeledPrice, PreCheckoutQuery
from aiogram.exceptions import TelegramAPIError

from app.services.payment_service import (
    PaymentProviderInterface,
    PaymentResult,
    PaymentStatus,
    PaymentProvider
)
from app.core.config import settings

logger = structlog.get_logger()


class TelegramStarsProvider(PaymentProviderInterface):
    """Провайдер для работы с Telegram Stars"""
    
    def __init__(self, bot: Bot):
        self.bot = bot
        # Telegram Stars использует внутреннюю валюту "XTR"
        self.currency = "XTR"
    
    async def create_payment(
        self,
        amount: Decimal,
        currency: str,
        description: str,
        return_url: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> PaymentResult:
        """
        Создает инвойс для оплаты Telegram Stars
        
        Args:
            amount: Сумма в звездах (Telegram Stars)
            currency: Валюта (должна быть XTR)
            description: Описание платежа
            return_url: Не используется для Telegram Stars
            metadata: Дополнительные данные
            
        Returns:
            PaymentResult: Результат создания платежа
        """
        if currency.upper() != "XTR":
            raise ValueError("Telegram Stars поддерживает только валюту XTR")
        
        # Генерируем уникальный payload для инвойса
        payment_id = str(uuid.uuid4())
        
        # Telegram Stars требует цену в копейках (1 звезда = 100 копеек)
        price_in_kopecks = int(amount * 100)
        
        try:
            # Создаем список цен
            prices = [LabeledPrice(label=description, amount=price_in_kopecks)]
            
            # Подготавливаем payload с метаданными
            payload_data = {
                "payment_id": payment_id,
                "metadata": metadata or {}
            }
            
            # Telegram ограничивает payload до 128 байт
            import json
            payload = json.dumps(payload_data)[:128]
            
            logger.info(
                "Создание инвойса Telegram Stars",
                payment_id=payment_id,
                amount=float(amount),
                description=description
            )
            
            # Возвращаем результат с данными для создания инвойса
            # Фактическая отправка инвойса происходит в обработчике бота
            return PaymentResult(
                payment_id=payment_id,
                status=PaymentStatus.PENDING,
                payment_url=None,  # Для Telegram Stars URL не нужен
                amount=amount,
                currency=self.currency,
                provider=PaymentProvider.TELEGRAM_STARS,
                metadata={
                    "prices": [{"label": description, "amount": price_in_kopecks}],
                    "payload": payload,
                    "description": description,
                    **(metadata or {})
                }
            )
            
        except Exception as e:
            logger.error("Ошибка создания инвойса Telegram Stars", error=str(e))
            raise Exception(f"Ошибка Telegram Stars: {str(e)}")
    
    async def send_invoice(
        self,
        chat_id: int,
        payment_result: PaymentResult,
        photo_url: Optional[str] = None
    ) -> bool:
        """
        Отправляет инвойс пользователю
        
        Args:
            chat_id: ID чата пользователя
            payment_result: Результат создания платежа
            photo_url: URL изображения для инвойса
            
        Returns:
            bool: True если инвойс отправлен успешно
        """
        try:
            metadata = payment_result.metadata
            prices = [
                LabeledPrice(
                    label=price["label"],
                    amount=price["amount"]
                ) for price in metadata["prices"]
            ]
            
            await self.bot.send_invoice(
                chat_id=chat_id,
                title="Оплата подписки VPN",
                description=metadata["description"],
                payload=metadata["payload"],
                provider_token="",  # Для Telegram Stars не нужен
                currency=self.currency,
                prices=prices,
                photo_url=photo_url,
                need_name=False,
                need_phone_number=False,
                need_email=False,
                need_shipping_address=False,
                send_phone_number_to_provider=False,
                send_email_to_provider=False,
                is_flexible=False
            )
            
            logger.info(
                "Отправлен инвойс Telegram Stars",
                payment_id=payment_result.payment_id,
                chat_id=chat_id
            )
            
            return True
            
        except TelegramAPIError as e:
            logger.error(
                "Ошибка отправки инвойса Telegram Stars",
                error=str(e),
                chat_id=chat_id
            )
            return False
    
    async def process_pre_checkout_query(
        self,
        pre_checkout_query: PreCheckoutQuery
    ) -> PaymentResult:
        """
        Обрабатывает pre-checkout query
        
        Args:
            pre_checkout_query: Запрос предварительной проверки
            
        Returns:
            PaymentResult: Результат обработки
        """
        try:
            # Парсим payload
            import json
            payload_data = json.loads(pre_checkout_query.invoice_payload)
            payment_id = payload_data.get("payment_id")
            
            # Подтверждаем платеж
            await self.bot.answer_pre_checkout_query(
                pre_checkout_query_id=pre_checkout_query.id,
                ok=True
            )
            
            logger.info(
                "Обработан pre-checkout query Telegram Stars",
                payment_id=payment_id,
                user_id=pre_checkout_query.from_user.id
            )
            
            return PaymentResult(
                payment_id=payment_id,
                status=PaymentStatus.PENDING,
                amount=Decimal(pre_checkout_query.total_amount) / 100,  # Конвертируем из копеек
                currency=pre_checkout_query.currency,
                provider=PaymentProvider.TELEGRAM_STARS,
                metadata=payload_data.get("metadata", {})
            )
            
        except Exception as e:
            logger.error("Ошибка обработки pre-checkout query", error=str(e))
            # Отклоняем платеж
            await self.bot.answer_pre_checkout_query(
                pre_checkout_query_id=pre_checkout_query.id,
                ok=False,
                error_message="Ошибка обработки платежа"
            )
            raise
    
    async def get_payment_status(self, payment_id: str) -> PaymentResult:
        """
        Получает статус платежа Telegram Stars
        
        Args:
            payment_id: ID платежа
            
        Returns:
            PaymentResult: Статус платежа
        """
        # Для Telegram Stars статус определяется через successful_payment
        # Здесь возвращаем базовую информацию
        logger.info("Запрос статуса платежа Telegram Stars", payment_id=payment_id)
        
        return PaymentResult(
            payment_id=payment_id,
            status=PaymentStatus.PENDING,
            provider=PaymentProvider.TELEGRAM_STARS
        )
    
    async def cancel_payment(self, payment_id: str) -> PaymentResult:
        """
        Отменяет платеж Telegram Stars
        
        Args:
            payment_id: ID платежа
            
        Returns:
            PaymentResult: Результат отмены
        """
        # Telegram Stars не поддерживает отмену платежей
        logger.warning("Telegram Stars не поддерживает отмену платежей", payment_id=payment_id)
        
        return PaymentResult(
            payment_id=payment_id,
            status=PaymentStatus.CANCELED,
            provider=PaymentProvider.TELEGRAM_STARS
        )
    
    async def refund_payment(
        self,
        payment_id: str,
        amount: Optional[Decimal] = None,
        reason: Optional[str] = None
    ) -> PaymentResult:
        """
        Возвращает платеж Telegram Stars
        
        Args:
            payment_id: ID платежа
            amount: Сумма возврата
            reason: Причина возврата
            
        Returns:
            PaymentResult: Результат возврата
        """
        try:
            # Используем метод refund_star_payment
            result = await self.bot.refund_star_payment(
                user_id=int(payment_id),  # Нужен user_id
                telegram_payment_charge_id=payment_id
            )
            
            if result:
                logger.info("Возврат Telegram Stars выполнен", payment_id=payment_id)
                return PaymentResult(
                    payment_id=payment_id,
                    status=PaymentStatus.SUCCEEDED,
                    amount=amount,
                    currency=self.currency,
                    provider=PaymentProvider.TELEGRAM_STARS
                )
            else:
                raise Exception("Не удалось выполнить возврат")
                
        except Exception as e:
            logger.error("Ошибка возврата Telegram Stars", error=str(e))
            raise Exception(f"Ошибка возврата Telegram Stars: {str(e)}")
    
    def validate_webhook(self, headers: Dict[str, str], body: bytes) -> bool:
        """
        Валидирует webhook от Telegram
        
        Args:
            headers: HTTP заголовки
            body: Тело запроса
            
        Returns:
            bool: True если webhook валидный
        """
        # Для Telegram Stars валидация происходит на уровне aiogram
        return True
    
    async def process_webhook(
        self,
        headers: Dict[str, str],
        body: Dict[str, Any]
    ) -> Optional[PaymentResult]:
        """
        Обрабатывает webhook от Telegram
        
        Args:
            headers: HTTP заголовки
            body: Тело запроса
            
        Returns:
            PaymentResult: Обновленный статус платежа
        """
        # Для Telegram Stars обработка происходит через aiogram handlers
        # Этот метод используется для совместимости с интерфейсом
        return None
    
    async def process_successful_payment(
        self,
        successful_payment_data: Dict[str, Any]
    ) -> PaymentResult:
        """
        Обрабатывает успешный платеж
        
        Args:
            successful_payment_data: Данные успешного платежа
            
        Returns:
            PaymentResult: Результат обработки
        """
        try:
            # Парсим payload
            import json
            payload_data = json.loads(successful_payment_data.get("invoice_payload", "{}"))
            payment_id = payload_data.get("payment_id")
            
            amount = Decimal(successful_payment_data.get("total_amount", 0)) / 100
            currency = successful_payment_data.get("currency", self.currency)
            
            logger.info(
                "Обработан успешный платеж Telegram Stars",
                payment_id=payment_id,
                amount=float(amount)
            )
            
            return PaymentResult(
                payment_id=payment_id,
                status=PaymentStatus.SUCCEEDED,
                amount=amount,
                currency=currency,
                provider=PaymentProvider.TELEGRAM_STARS,
                metadata={
                    "telegram_payment_charge_id": successful_payment_data.get("telegram_payment_charge_id"),
                    "provider_payment_charge_id": successful_payment_data.get("provider_payment_charge_id"),
                    **payload_data.get("metadata", {})
                }
            )
            
        except Exception as e:
            logger.error("Ошибка обработки успешного платежа", error=str(e))
            raise
