"""
Вспомогательные функции
"""

from datetime import datetime, timezone
from typing import Optional


def create_marzban_username(telegram_id: int, username: Optional[str] = None) -> str:
    """
    Создает имя пользователя для Marzban на основе Telegram данных
    
    Args:
        telegram_id: Telegram ID пользователя
        username: Telegram username (опционально)
        
    Returns:
        Имя пользователя для Marzban
    """
    if username:
        return f"tg_{username}_{telegram_id}"
    else:
        return f"tg_user_{telegram_id}"


def format_bytes(bytes_value: Optional[int]) -> str:
    """
    Форматирует количество байт в человекочитаемый формат
    
    Args:
        bytes_value: Количество байт
        
    Returns:
        Отформатированная строка
    """
    if bytes_value is None:
        return "∞"
    
    if bytes_value == 0:
        return "0 B"
    
    units = ["B", "KB", "MB", "GB", "TB", "PB"]
    size = float(bytes_value)
    unit_index = 0
    
    while size >= 1024 and unit_index < len(units) - 1:
        size /= 1024
        unit_index += 1
    
    if unit_index == 0:
        return f"{int(size)} {units[unit_index]}"
    else:
        return f"{size:.1f} {units[unit_index]}"


def format_datetime(dt: Optional[datetime]) -> str:
    """
    Форматирует datetime в человекочитаемый формат
    
    Args:
        dt: Объект datetime
        
    Returns:
        Отформатированная строка
    """
    if dt is None:
        return "Не указано"
    
    # Конвертируем в UTC если нет timezone info
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=timezone.utc)
    
    # Форматируем в московское время (UTC+3)
    moscow_dt = dt.replace(tzinfo=timezone.utc).astimezone(tz=None)
    return moscow_dt.strftime("%d.%m.%Y %H:%M")


def format_currency(amount: float, currency: str = "RUB") -> str:
    """
    Форматирует сумму в валюте
    
    Args:
        amount: Сумма
        currency: Валюта
        
    Returns:
        Отформатированная строка
    """
    currency_symbols = {
        "RUB": "₽",
        "USD": "$",
        "EUR": "€",
        "BTC": "₿",
        "ETH": "Ξ"
    }
    
    symbol = currency_symbols.get(currency, currency)
    
    if currency in ["BTC", "ETH"]:
        return f"{amount:.8f} {symbol}"
    else:
        return f"{amount:.2f} {symbol}"


def truncate_text(text: str, max_length: int = 100) -> str:
    """
    Обрезает текст до указанной длины
    
    Args:
        text: Исходный текст
        max_length: Максимальная длина
        
    Returns:
        Обрезанный текст
    """
    if len(text) <= max_length:
        return text
    
    return text[:max_length - 3] + "..."


def escape_markdown(text: str) -> str:
    """
    Экранирует специальные символы для Markdown
    
    Args:
        text: Исходный текст
        
    Returns:
        Экранированный текст
    """
    special_chars = ['_', '*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!']
    
    for char in special_chars:
        text = text.replace(char, f'\\{char}')
    
    return text


def validate_telegram_id(telegram_id: int) -> bool:
    """
    Проверяет валидность Telegram ID
    
    Args:
        telegram_id: Telegram ID для проверки
        
    Returns:
        True если ID валидный
    """
    # Telegram ID должен быть положительным числом
    # и находиться в разумных пределах
    return isinstance(telegram_id, int) and 1 <= telegram_id <= 999999999999


def generate_config_filename(username: str, plan_name: str) -> str:
    """
    Генерирует имя файла конфигурации
    
    Args:
        username: Имя пользователя
        plan_name: Название плана
        
    Returns:
        Имя файла
    """
    # Убираем специальные символы и пробелы
    safe_username = "".join(c for c in username if c.isalnum() or c in "-_")
    safe_plan = "".join(c for c in plan_name if c.isalnum() or c in "-_")
    
    return f"{safe_username}_{safe_plan}_config.txt"


def calculate_discount(original_price: float, discount_percent: float) -> tuple[float, float]:
    """
    Вычисляет размер скидки и итоговую цену
    
    Args:
        original_price: Исходная цена
        discount_percent: Процент скидки
        
    Returns:
        Кортеж (размер_скидки, итоговая_цена)
    """
    discount_amount = original_price * (discount_percent / 100)
    final_price = original_price - discount_amount
    
    return discount_amount, max(0, final_price)


def format_traffic_speed(bytes_per_second: int) -> str:
    """
    Форматирует скорость трафика
    
    Args:
        bytes_per_second: Байт в секунду
        
    Returns:
        Отформатированная строка скорости
    """
    if bytes_per_second == 0:
        return "0 B/s"
    
    units = ["B/s", "KB/s", "MB/s", "GB/s"]
    speed = float(bytes_per_second)
    unit_index = 0
    
    while speed >= 1024 and unit_index < len(units) - 1:
        speed /= 1024
        unit_index += 1
    
    if unit_index == 0:
        return f"{int(speed)} {units[unit_index]}"
    else:
        return f"{speed:.1f} {units[unit_index]}"


def get_user_display_name(first_name: Optional[str], last_name: Optional[str], username: Optional[str]) -> str:
    """
    Получает отображаемое имя пользователя
    
    Args:
        first_name: Имя
        last_name: Фамилия
        username: Username
        
    Returns:
        Отображаемое имя
    """
    if first_name and last_name:
        return f"{first_name} {last_name}"
    elif first_name:
        return first_name
    elif username:
        return f"@{username}"
    else:
        return "Пользователь"


def is_valid_email(email: str) -> bool:
    """
    Простая проверка валидности email
    
    Args:
        email: Email для проверки
        
    Returns:
        True если email валидный
    """
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def generate_invoice_number() -> str:
    """
    Генерирует номер счета
    
    Returns:
        Номер счета
    """
    import random
    import string
    
    timestamp = int(datetime.now().timestamp())
    random_part = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
    
    return f"INV-{timestamp}-{random_part}"
