"""
Обработчики основного бота
"""

from aiogram import Dispatcher
from .start import start_router
from .subscription import subscription_router


def register_handlers(dp: Dispatcher) -> None:
    """
    Регистрация всех обработчиков

    Args:
        dp: Диспетчер aiogram
    """
    # Регистрируем роутеры в порядке приоритета
    dp.include_router(start_router)
    dp.include_router(subscription_router)
