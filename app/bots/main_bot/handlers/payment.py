"""
Обработчики платежей для основного бота
"""

from typing import Any, Dict, Optional
from decimal import Decimal
import structlog

from aiogram import Router, F
from aiogram.types import (
    Message, CallbackQuery, PreCheckoutQuery,
    InlineKeyboardMarkup, InlineKeyboardButton
)
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup

from app.database.database import get_async_session
from app.database.crud import get_user_by_telegram_id, get_subscription_plan_by_id
from app.services.payment_service import PaymentService, PaymentProvider
from app.services.providers import YooKassaProvider, CryptomusProvider, TelegramStarsProvider
from app.bots.main_bot.keyboards.inline import (
    create_payment_methods_keyboard,
    create_payment_confirmation_keyboard
)
from app.core.config import settings

logger = structlog.get_logger()
payment_router = Router()


class PaymentStates(StatesGroup):
    """Состояния процесса оплаты"""
    selecting_method = State()
    confirming_payment = State()
    processing_payment = State()


@payment_router.callback_query(F.data.startswith("buy_plan:"))
async def start_payment_process(callback: CallbackQuery, state: FSMContext):
    """Начинает процесс оплаты тарифного плана"""
    try:
        plan_id = int(callback.data.split(":")[1])
        
        async with get_async_session() as session:
            # Получаем информацию о тарифном плане
            plan = await get_subscription_plan_by_id(session, plan_id)
            if not plan:
                await callback.answer("❌ Тарифный план не найден", show_alert=True)
                return
            
            # Получаем пользователя
            user = await get_user_by_telegram_id(session, callback.from_user.id)
            if not user:
                await callback.answer("❌ Пользователь не найден", show_alert=True)
                return
            
            # Сохраняем данные в состоянии
            await state.update_data(
                plan_id=plan_id,
                plan_name=plan.name,
                plan_price=float(plan.price),
                plan_currency="RUB",
                user_id=user.telegram_id
            )
            
            # Показываем методы оплаты
            keyboard = create_payment_methods_keyboard()
            
            text = (
                f"💳 <b>Оплата тарифного плана</b>\n\n"
                f"📦 <b>План:</b> {plan.name}\n"
                f"💰 <b>Стоимость:</b> {plan.price} ₽\n"
                f"⏱ <b>Срок:</b> {plan.duration_days} дней\n\n"
                f"Выберите способ оплаты:"
            )
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await state.set_state(PaymentStates.selecting_method)
            
    except Exception as e:
        logger.error("Ошибка начала процесса оплаты", error=str(e))
        await callback.answer("❌ Произошла ошибка", show_alert=True)


@payment_router.callback_query(F.data.startswith("payment_method:"))
async def select_payment_method(callback: CallbackQuery, state: FSMContext):
    """Обрабатывает выбор метода оплаты"""
    try:
        method = callback.data.split(":")[1]
        data = await state.get_data()
        
        # Сохраняем выбранный метод
        await state.update_data(payment_method=method)
        
        # Создаем клавиатуру подтверждения
        keyboard = create_payment_confirmation_keyboard()
        
        # Определяем текст в зависимости от метода
        method_names = {
            "yookassa": "💳 Банковская карта (ЮKassa)",
            "cryptomus": "₿ Криптовалюта (Cryptomus)",
            "telegram_stars": "⭐ Telegram Stars"
        }
        
        text = (
            f"✅ <b>Подтверждение оплаты</b>\n\n"
            f"📦 <b>План:</b> {data['plan_name']}\n"
            f"💰 <b>Стоимость:</b> {data['plan_price']} ₽\n"
            f"💳 <b>Способ оплаты:</b> {method_names.get(method, method)}\n\n"
            f"Подтвердите создание платежа:"
        )
        
        await callback.message.edit_text(text, reply_markup=keyboard)
        await state.set_state(PaymentStates.confirming_payment)
        
    except Exception as e:
        logger.error("Ошибка выбора метода оплаты", error=str(e))
        await callback.answer("❌ Произошла ошибка", show_alert=True)


@payment_router.callback_query(F.data == "confirm_payment")
async def confirm_payment(callback: CallbackQuery, state: FSMContext):
    """Подтверждает и создает платеж"""
    try:
        data = await state.get_data()
        method = data.get("payment_method")
        
        await callback.message.edit_text("⏳ Создание платежа...")
        await state.set_state(PaymentStates.processing_payment)
        
        async with get_async_session() as session:
            # Инициализируем сервис платежей
            payment_service = PaymentService(session)
            
            # Регистрируем провайдеров
            await _register_payment_providers(payment_service, callback.bot)
            
            # Определяем провайдера и валюту
            provider_map = {
                "yookassa": (PaymentProvider.YOOKASSA, "RUB"),
                "cryptomus": (PaymentProvider.CRYPTOMUS, "USDT"),
                "telegram_stars": (PaymentProvider.TELEGRAM_STARS, "XTR")
            }
            
            provider, currency = provider_map.get(method, (PaymentProvider.YOOKASSA, "RUB"))
            
            # Конвертируем сумму для разных валют
            amount = Decimal(str(data["plan_price"]))
            if currency == "USDT":
                # Примерный курс RUB -> USDT (в реальности нужно получать актуальный курс)
                amount = amount / Decimal("90")  # 1 USDT ≈ 90 RUB
            elif currency == "XTR":
                # Telegram Stars: 1 звезда ≈ 2 рубля
                amount = amount / Decimal("2")
            
            # Создаем платеж
            result = await payment_service.create_payment(
                provider=provider,
                amount=amount,
                currency=currency,
                description=f"Оплата тарифного плана {data['plan_name']}",
                user_id=data["user_id"],
                subscription_plan_id=data["plan_id"],
                return_url=settings.PAYMENT_RETURN_URL,
                metadata={
                    "plan_id": data["plan_id"],
                    "plan_name": data["plan_name"],
                    "telegram_user_id": callback.from_user.id
                }
            )
            
            # Обрабатываем результат в зависимости от провайдера
            if provider == PaymentProvider.TELEGRAM_STARS:
                # Для Telegram Stars отправляем инвойс
                stars_provider = payment_service.providers[provider]
                success = await stars_provider.send_invoice(
                    chat_id=callback.from_user.id,
                    payment_result=result
                )
                
                if success:
                    text = (
                        f"⭐ <b>Инвойс отправлен!</b>\n\n"
                        f"Оплатите инвойс с помощью Telegram Stars.\n"
                        f"После оплаты подписка будет активирована автоматически."
                    )
                else:
                    text = "❌ Ошибка отправки инвойса"
                    
                await callback.message.edit_text(text)
                
            else:
                # Для внешних провайдеров показываем ссылку
                keyboard = InlineKeyboardMarkup(inline_keyboard=[
                    [InlineKeyboardButton(
                        text="💳 Перейти к оплате",
                        url=result.payment_url
                    )],
                    [InlineKeyboardButton(
                        text="🔄 Проверить статус",
                        callback_data=f"check_payment:{result.payment_id}"
                    )]
                ])
                
                text = (
                    f"💳 <b>Платеж создан!</b>\n\n"
                    f"💰 <b>Сумма:</b> {amount} {currency}\n"
                    f"🆔 <b>ID платежа:</b> <code>{result.payment_id}</code>\n\n"
                    f"Нажмите кнопку ниже для перехода к оплате:"
                )
                
                await callback.message.edit_text(text, reply_markup=keyboard)
            
            # Сохраняем ID платежа в состоянии
            await state.update_data(payment_id=result.payment_id)
            
    except Exception as e:
        logger.error("Ошибка создания платежа", error=str(e))
        await callback.message.edit_text("❌ Ошибка создания платежа. Попробуйте позже.")
        await state.clear()


@payment_router.callback_query(F.data.startswith("check_payment:"))
async def check_payment_status(callback: CallbackQuery, state: FSMContext):
    """Проверяет статус платежа"""
    try:
        payment_id = callback.data.split(":")[1]
        data = await state.get_data()
        method = data.get("payment_method", "yookassa")
        
        async with get_async_session() as session:
            payment_service = PaymentService(session)
            await _register_payment_providers(payment_service, callback.bot)
            
            # Определяем провайдера
            provider_map = {
                "yookassa": PaymentProvider.YOOKASSA,
                "cryptomus": PaymentProvider.CRYPTOMUS,
                "telegram_stars": PaymentProvider.TELEGRAM_STARS
            }
            provider = provider_map.get(method, PaymentProvider.YOOKASSA)
            
            # Получаем статус платежа
            result = await payment_service.get_payment_status(payment_id, provider)
            
            status_texts = {
                "pending": "⏳ Ожидает оплаты",
                "waiting_for_capture": "⏳ Ожидает подтверждения",
                "succeeded": "✅ Оплачен",
                "canceled": "❌ Отменен",
                "failed": "❌ Ошибка оплаты"
            }
            
            status_text = status_texts.get(result.status.value, "❓ Неизвестный статус")
            
            await callback.answer(f"Статус платежа: {status_text}")
            
            if result.status.value == "succeeded":
                await callback.message.edit_text(
                    f"✅ <b>Платеж успешно завершен!</b>\n\n"
                    f"Ваша подписка будет активирована в течение нескольких минут."
                )
                await state.clear()
                
    except Exception as e:
        logger.error("Ошибка проверки статуса платежа", error=str(e))
        await callback.answer("❌ Ошибка проверки статуса")


@payment_router.callback_query(F.data == "cancel_payment")
async def cancel_payment(callback: CallbackQuery, state: FSMContext):
    """Отменяет процесс оплаты"""
    await callback.message.edit_text("❌ Оплата отменена")
    await state.clear()


@payment_router.pre_checkout_query()
async def process_pre_checkout_query(pre_checkout_query: PreCheckoutQuery):
    """Обрабатывает pre-checkout query для Telegram Stars"""
    try:
        async with get_async_session() as session:
            payment_service = PaymentService(session)
            
            # Создаем провайдер Telegram Stars
            stars_provider = TelegramStarsProvider(pre_checkout_query.bot)
            payment_service.register_provider(PaymentProvider.TELEGRAM_STARS, stars_provider)
            
            # Обрабатываем pre-checkout query
            await stars_provider.process_pre_checkout_query(pre_checkout_query)
            
    except Exception as e:
        logger.error("Ошибка обработки pre-checkout query", error=str(e))
        await pre_checkout_query.bot.answer_pre_checkout_query(
            pre_checkout_query_id=pre_checkout_query.id,
            ok=False,
            error_message="Ошибка обработки платежа"
        )


@payment_router.message(F.successful_payment)
async def process_successful_payment(message: Message):
    """Обрабатывает успешный платеж Telegram Stars"""
    try:
        async with get_async_session() as session:
            payment_service = PaymentService(session)
            
            # Создаем провайдер Telegram Stars
            stars_provider = TelegramStarsProvider(message.bot)
            payment_service.register_provider(PaymentProvider.TELEGRAM_STARS, stars_provider)
            
            # Обрабатываем успешный платеж
            payment_data = {
                "invoice_payload": message.successful_payment.invoice_payload,
                "total_amount": message.successful_payment.total_amount,
                "currency": message.successful_payment.currency,
                "telegram_payment_charge_id": message.successful_payment.telegram_payment_charge_id,
                "provider_payment_charge_id": message.successful_payment.provider_payment_charge_id
            }
            
            result = await stars_provider.process_successful_payment(payment_data)
            
            # Активируем подписку
            from app.services.subscription_service import SubscriptionService
            subscription_service = SubscriptionService(session)
            
            # Получаем метаданные платежа
            metadata = result.metadata
            plan_id = metadata.get("plan_id")
            
            if plan_id:
                await subscription_service.create_subscription(
                    user_id=message.from_user.id,
                    plan_id=int(plan_id)
                )
                
                await message.answer(
                    f"🎉 <b>Платеж успешно завершен!</b>\n\n"
                    f"⭐ Оплачено: {result.amount} Telegram Stars\n"
                    f"✅ Подписка активирована автоматически.\n\n"
                    f"Используйте /status для просмотра информации о подписке."
                )
            else:
                await message.answer("✅ Платеж получен, но не удалось активировать подписку. Обратитесь в поддержку.")
                
    except Exception as e:
        logger.error("Ошибка обработки успешного платежа", error=str(e))
        await message.answer("❌ Ошибка обработки платежа. Обратитесь в поддержку.")


async def _register_payment_providers(payment_service: PaymentService, bot) -> None:
    """Регистрирует всех платежных провайдеров"""
    # ЮKassa
    if settings.YOOKASSA_SHOP_ID and settings.YOOKASSA_SECRET_KEY:
        yookassa_provider = YooKassaProvider(
            shop_id=settings.YOOKASSA_SHOP_ID,
            secret_key=settings.YOOKASSA_SECRET_KEY
        )
        payment_service.register_provider(PaymentProvider.YOOKASSA, yookassa_provider)
    
    # Cryptomus
    if settings.CRYPTOMUS_MERCHANT_ID and settings.CRYPTOMUS_API_KEY:
        cryptomus_provider = CryptomusProvider(
            merchant_id=settings.CRYPTOMUS_MERCHANT_ID,
            api_key=settings.CRYPTOMUS_API_KEY
        )
        payment_service.register_provider(PaymentProvider.CRYPTOMUS, cryptomus_provider)
    
    # Telegram Stars
    stars_provider = TelegramStarsProvider(bot)
    payment_service.register_provider(PaymentProvider.TELEGRAM_STARS, stars_provider)
