"""
Обработчики команд для управления подписками
"""

from aiogram import Router, F
from aiogram.types import Message, CallbackQuery, InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.filters import Command
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
import structlog

from app.database.database import get_db
from app.database.crud import get_active_subscription_plans, get_user_by_telegram_id
from app.services.subscription_service import SubscriptionService
from app.services.notification_service import NotificationService
from app.integrations.marzban import MarzbanClient
from app.utils.helpers import format_bytes, format_datetime, format_currency
from app.bots.main_bot.keyboards.inline import create_subscription_plans_keyboard, create_subscription_management_keyboard

logger = structlog.get_logger()

# Создаем роутер для обработчиков подписок
subscription_router = Router()


class SubscriptionStates(StatesGroup):
    """Состояния для управления подписками"""
    selecting_plan = State()
    confirming_purchase = State()


@subscription_router.message(Command("plans"))
async def cmd_plans(message: Message, state: FSMContext):
    """
    Команда /plans - показывает доступные тарифные планы
    """
    logger.info("Команда /plans", user_id=message.from_user.id)
    
    try:
        async with get_db() as db:
            plans = await get_active_subscription_plans(db)
            
            if not plans:
                await message.answer(
                    "❌ В данный момент нет доступных тарифных планов.\n"
                    "Попробуйте позже или обратитесь в поддержку."
                )
                return
            
            # Формируем сообщение с планами
            text = "📋 <b>Доступные тарифные планы:</b>\n\n"
            
            for plan in plans:
                traffic_limit = format_bytes(plan.traffic_limit) if plan.traffic_limit else "Безлимитный"
                price = format_currency(plan.price, plan.currency)
                
                text += f"🔹 <b>{plan.name}</b>\n"
                text += f"💰 Цена: {price}\n"
                text += f"📅 Срок: {plan.duration_days} дней\n"
                text += f"📊 Трафик: {traffic_limit}\n"
                text += f"📱 Устройств: {plan.max_devices}\n"
                
                if plan.description:
                    text += f"📝 {plan.description}\n"
                
                text += "\n"
            
            text += "Выберите подходящий план:"
            
            # Создаем клавиатуру с планами
            keyboard = create_subscription_plans_keyboard(plans)
            
            await message.answer(text, reply_markup=keyboard, parse_mode="HTML")
            await state.set_state(SubscriptionStates.selecting_plan)
            
    except Exception as e:
        logger.error("Ошибка получения планов", error=str(e), user_id=message.from_user.id)
        await message.answer(
            "❌ Произошла ошибка при получении тарифных планов.\n"
            "Попробуйте позже или обратитесь в поддержку."
        )


@subscription_router.callback_query(F.data.startswith("plan_"))
async def process_plan_selection(callback: CallbackQuery, state: FSMContext):
    """
    Обработка выбора тарифного плана
    """
    plan_id = int(callback.data.split("_")[1])
    logger.info("Выбор плана", plan_id=plan_id, user_id=callback.from_user.id)
    
    try:
        async with get_db() as db:
            # Получаем план
            from app.database.crud import get_subscription_plan_by_id
            plan = await get_subscription_plan_by_id(db, plan_id)
            
            if not plan:
                await callback.answer("❌ План не найден", show_alert=True)
                return
            
            # Проверяем, есть ли у пользователя активная подписка
            subscription_service = SubscriptionService(db)
            active_subscription = await subscription_service.get_user_active_subscription(
                callback.from_user.id
            )
            
            if active_subscription:
                await callback.answer(
                    "❌ У вас уже есть активная подписка. "
                    "Используйте /status для управления текущей подпиской.",
                    show_alert=True
                )
                return
            
            # Формируем сообщение с подтверждением
            traffic_limit = format_bytes(plan.traffic_limit) if plan.traffic_limit else "Безлимитный"
            price = format_currency(plan.price, plan.currency)
            
            text = f"🛒 <b>Подтверждение покупки</b>\n\n"
            text += f"📋 <b>План:</b> {plan.name}\n"
            text += f"💰 <b>Цена:</b> {price}\n"
            text += f"📅 <b>Срок:</b> {plan.duration_days} дней\n"
            text += f"📊 <b>Трафик:</b> {traffic_limit}\n"
            text += f"📱 <b>Устройств:</b> {plan.max_devices}\n\n"
            
            if plan.description:
                text += f"📝 <b>Описание:</b> {plan.description}\n\n"
            
            text += "Подтвердите покупку:"
            
            # Создаем клавиатуру подтверждения
            keyboard = InlineKeyboardMarkup(inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="✅ Подтвердить покупку",
                        callback_data=f"confirm_purchase_{plan_id}"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="❌ Отмена",
                        callback_data="cancel_purchase"
                    )
                ]
            ])
            
            await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
            await state.update_data(selected_plan_id=plan_id)
            await state.set_state(SubscriptionStates.confirming_purchase)
            
    except Exception as e:
        logger.error("Ошибка выбора плана", error=str(e), plan_id=plan_id, user_id=callback.from_user.id)
        await callback.answer("❌ Произошла ошибка", show_alert=True)


@subscription_router.callback_query(F.data.startswith("confirm_purchase_"))
async def process_purchase_confirmation(callback: CallbackQuery, state: FSMContext):
    """
    Обработка подтверждения покупки
    """
    plan_id = int(callback.data.split("_")[2])
    logger.info("Подтверждение покупки", plan_id=plan_id, user_id=callback.from_user.id)
    
    try:
        async with get_db() as db:
            # Создаем подписку
            subscription_service = SubscriptionService(db, MarzbanClient())
            
            subscription = await subscription_service.create_subscription(
                user_telegram_id=callback.from_user.id,
                plan_id=plan_id
            )
            
            # Отправляем уведомление
            notification_service = NotificationService(callback.bot)
            user = await get_user_by_telegram_id(db, callback.from_user.id)
            
            try:
                await notification_service.send_subscription_created(user, subscription)
            except Exception as e:
                logger.warning("Ошибка отправки уведомления", error=str(e))
            
            # Обновляем сообщение
            text = "✅ <b>Подписка успешно создана!</b>\n\n"
            text += f"🆔 <b>ID подписки:</b> {subscription.id}\n"
            text += f"📋 <b>План:</b> {subscription.plan.name}\n"
            text += f"📅 <b>Действует до:</b> {format_datetime(subscription.end_date)}\n\n"
            text += "Используйте команду /config для получения конфигурации."
            
            await callback.message.edit_text(text, parse_mode="HTML")
            await state.clear()
            
    except Exception as e:
        logger.error("Ошибка создания подписки", error=str(e), plan_id=plan_id, user_id=callback.from_user.id)
        await callback.answer("❌ Произошла ошибка при создании подписки", show_alert=True)


@subscription_router.callback_query(F.data == "cancel_purchase")
async def process_purchase_cancellation(callback: CallbackQuery, state: FSMContext):
    """
    Обработка отмены покупки
    """
    await callback.message.edit_text(
        "❌ Покупка отменена.\n\n"
        "Используйте команду /plans для выбора другого плана."
    )
    await state.clear()


@subscription_router.message(Command("status"))
async def cmd_status(message: Message):
    """
    Команда /status - показывает статус текущей подписки
    """
    logger.info("Команда /status", user_id=message.from_user.id)
    
    try:
        async with get_db() as db:
            subscription_service = SubscriptionService(db, MarzbanClient())
            
            # Получаем активную подписку
            subscription = await subscription_service.get_user_active_subscription(
                message.from_user.id
            )
            
            if not subscription:
                await message.answer(
                    "❌ У вас нет активной подписки.\n\n"
                    "Используйте команду /plans для выбора тарифного плана."
                )
                return
            
            # Получаем подробную информацию
            info = await subscription_service.get_subscription_info(subscription.id)
            
            # Формируем сообщение
            text = f"📊 <b>Статус подписки</b>\n\n"
            text += f"🆔 <b>ID:</b> {info['id']}\n"
            text += f"📋 <b>План:</b> {info['plan_name']}\n"
            text += f"📅 <b>Действует до:</b> {format_datetime(info['end_date'])}\n"
            text += f"⏰ <b>Осталось дней:</b> {info['days_remaining']}\n\n"
            
            # Информация о трафике
            if info['traffic_limit']:
                used_traffic = format_bytes(info['traffic_used'])
                total_traffic = format_bytes(info['traffic_limit'])
                usage_percent = info['traffic_usage_percent']
                
                text += f"📊 <b>Трафик:</b>\n"
                text += f"   Использовано: {used_traffic} из {total_traffic}\n"
                text += f"   Процент: {usage_percent:.1f}%\n\n"
            else:
                text += f"📊 <b>Трафик:</b> Безлимитный\n\n"
            
            # Статус
            status_emoji = {
                'active': '✅',
                'expired': '❌',
                'suspended': '⏸️',
                'cancelled': '🚫'
            }
            
            text += f"🔄 <b>Статус:</b> {status_emoji.get(info['status'], '❓')} {info['status'].title()}\n"
            
            # Предупреждения
            if info['is_expired']:
                text += "\n⚠️ <b>Подписка истекла!</b> Используйте /renew для продления."
            elif info['days_remaining'] <= 3:
                text += f"\n⚠️ <b>Подписка истекает через {info['days_remaining']} дней!</b>"
            
            if info['is_quota_exceeded']:
                text += "\n⚠️ <b>Превышен лимит трафика!</b>"
            
            # Создаем клавиатуру управления
            keyboard = create_subscription_management_keyboard(subscription.id)
            
            await message.answer(text, reply_markup=keyboard, parse_mode="HTML")
            
    except Exception as e:
        logger.error("Ошибка получения статуса", error=str(e), user_id=message.from_user.id)
        await message.answer(
            "❌ Произошла ошибка при получении статуса подписки.\n"
            "Попробуйте позже или обратитесь в поддержку."
        )


@subscription_router.message(Command("config"))
async def cmd_config(message: Message):
    """
    Команда /config - отправляет конфигурационный файл
    """
    logger.info("Команда /config", user_id=message.from_user.id)
    
    try:
        async with get_db() as db:
            subscription_service = SubscriptionService(db, MarzbanClient())
            
            # Получаем активную подписку
            subscription = await subscription_service.get_user_active_subscription(
                message.from_user.id
            )
            
            if not subscription:
                await message.answer(
                    "❌ У вас нет активной подписки.\n\n"
                    "Используйте команду /plans для выбора тарифного плана."
                )
                return
            
            if subscription.status.value != 'active':
                await message.answer(
                    "❌ Ваша подписка неактивна.\n\n"
                    "Используйте команду /status для получения подробной информации."
                )
                return
            
            # Получаем конфигурацию
            if subscription.config_url:
                text = f"📱 <b>Конфигурация VPN</b>\n\n"
                text += f"📋 <b>План:</b> {subscription.plan.name}\n"
                text += f"📅 <b>Действует до:</b> {format_datetime(subscription.end_date)}\n\n"
                text += f"🔗 <b>Ссылка на конфигурацию:</b>\n"
                text += f"<code>{subscription.config_url}</code>\n\n"
                text += "📱 Скопируйте ссылку и импортируйте её в ваше VPN приложение."
                
                await message.answer(text, parse_mode="HTML")
            else:
                await message.answer(
                    "❌ Конфигурация пока не готова.\n"
                    "Попробуйте через несколько минут или обратитесь в поддержку."
                )
                
    except Exception as e:
        logger.error("Ошибка получения конфигурации", error=str(e), user_id=message.from_user.id)
        await message.answer(
            "❌ Произошла ошибка при получении конфигурации.\n"
            "Попробуйте позже или обратитесь в поддержку."
        )
