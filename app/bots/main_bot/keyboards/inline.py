"""
Инлайн клавиатуры для бота
"""

from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from typing import List

from app.database.models import SubscriptionPlan
from app.utils.helpers import format_currency


def create_main_menu_keyboard() -> InlineKeyboardMarkup:
    """Создает главное меню"""
    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [
            InlineKeyboardButton(text="📋 Тарифные планы", callback_data="plans"),
            InlineKeyboardButton(text="📊 Мой статус", callback_data="status")
        ],
        [
            InlineKeyboardButton(text="📱 Конфигурация", callback_data="config"),
            InlineKeyboardButton(text="💰 Баланс", callback_data="balance")
        ],
        [
            InlineKeyboardButton(text="❓ Помощь", callback_data="help"),
            InlineKeyboardButton(text="🎫 Поддержка", callback_data="support")
        ]
    ])
    return keyboard


def create_subscription_plans_keyboard(plans: List[SubscriptionPlan]) -> InlineKeyboardMarkup:
    """
    Создает клавиатуру с тарифными планами
    
    Args:
        plans: Список тарифных планов
        
    Returns:
        InlineKeyboardMarkup: Клавиатура с планами
    """
    buttons = []
    
    for plan in plans:
        price_text = format_currency(plan.price, plan.currency)
        button_text = f"{plan.name} - {price_text}"
        
        buttons.append([
            InlineKeyboardButton(
                text=button_text,
                callback_data=f"plan_{plan.id}"
            )
        ])
    
    # Добавляем кнопку "Назад"
    buttons.append([
        InlineKeyboardButton(text="🔙 Назад", callback_data="back_to_menu")
    ])
    
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def create_subscription_management_keyboard(subscription_id: int) -> InlineKeyboardMarkup:
    """
    Создает клавиатуру для управления подпиской
    
    Args:
        subscription_id: ID подписки
        
    Returns:
        InlineKeyboardMarkup: Клавиатура управления
    """
    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [
            InlineKeyboardButton(
                text="📱 Получить конфигурацию",
                callback_data=f"get_config_{subscription_id}"
            )
        ],
        [
            InlineKeyboardButton(
                text="🔄 Обновить статус",
                callback_data=f"refresh_status_{subscription_id}"
            ),
            InlineKeyboardButton(
                text="📊 Статистика",
                callback_data=f"stats_{subscription_id}"
            )
        ],
        [
            InlineKeyboardButton(
                text="💳 Продлить",
                callback_data=f"renew_{subscription_id}"
            ),
            InlineKeyboardButton(
                text="⚙️ Настройки",
                callback_data=f"settings_{subscription_id}"
            )
        ],
        [
            InlineKeyboardButton(text="🔙 Главное меню", callback_data="main_menu")
        ]
    ])
    return keyboard


def create_payment_keyboard(plan_id: int, amount: float, currency: str = "RUB") -> InlineKeyboardMarkup:
    """
    Создает клавиатуру для выбора способа оплаты
    
    Args:
        plan_id: ID тарифного плана
        amount: Сумма к оплате
        currency: Валюта
        
    Returns:
        InlineKeyboardMarkup: Клавиатура оплаты
    """
    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [
            InlineKeyboardButton(
                text="💳 Банковская карта",
                callback_data=f"pay_card_{plan_id}_{amount}_{currency}"
            )
        ],
        [
            InlineKeyboardButton(
                text="🪙 Криптовалюта",
                callback_data=f"pay_crypto_{plan_id}_{amount}_{currency}"
            )
        ],
        [
            InlineKeyboardButton(
                text="⭐ Telegram Stars",
                callback_data=f"pay_stars_{plan_id}_{amount}_{currency}"
            )
        ],
        [
            InlineKeyboardButton(text="❌ Отмена", callback_data="cancel_payment")
        ]
    ])
    return keyboard


def create_confirmation_keyboard(action: str, item_id: int) -> InlineKeyboardMarkup:
    """
    Создает клавиатуру подтверждения действия
    
    Args:
        action: Действие для подтверждения
        item_id: ID элемента
        
    Returns:
        InlineKeyboardMarkup: Клавиатура подтверждения
    """
    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [
            InlineKeyboardButton(
                text="✅ Подтвердить",
                callback_data=f"confirm_{action}_{item_id}"
            ),
            InlineKeyboardButton(
                text="❌ Отмена",
                callback_data=f"cancel_{action}_{item_id}"
            )
        ]
    ])
    return keyboard
