"""
Модели базы данных
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, List
from enum import Enum
from sqlalchemy import (
    Column, Integer, BigInteger, String, Text, DECIMAL, Boolean,
    DateTime, ForeignKey, JSON, Index, Enum as SQLEnum
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.database.database import Base


class SubscriptionStatus(str, Enum):
    """Статусы подписки"""
    PENDING = "pending"
    ACTIVE = "active"
    EXPIRED = "expired"
    SUSPENDED = "suspended"
    CANCELLED = "cancelled"


class TransactionStatus(str, Enum):
    """Статусы транзакции"""
    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class User(Base):
    """Модель пользователя"""
    
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, autoincrement=True, index=True)
    telegram_id = Column(BigInteger, unique=True, nullable=False, index=True)
    username = Column(String(255), nullable=True)
    first_name = Column(String(255), nullable=True)
    last_name = Column(String(255), nullable=True)
    language_code = Column(String(10), default='ru')
    balance = Column(DECIMAL(10, 2), default=0.00)
    referral_code = Column(String(50), unique=True, nullable=True, index=True)
    referred_by = Column(BigInteger, ForeignKey('users.telegram_id'), nullable=True)
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    is_premium = Column(Boolean, default=False)  # Премиум статус
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    subscriptions = relationship("UserSubscription", back_populates="user", cascade="all, delete-orphan")
    transactions = relationship("Transaction", back_populates="user", cascade="all, delete-orphan")
    support_tickets = relationship("SupportTicket", back_populates="user", cascade="all, delete-orphan", foreign_keys="SupportTicket.user_id")
    referrer = relationship("User", remote_side=[telegram_id])
    
    # Indexes
    __table_args__ = (
        Index('idx_user_telegram_id', 'telegram_id'),
        Index('idx_user_referral_code', 'referral_code'),
        Index('idx_user_active', 'is_active'),
    )
    
    def __repr__(self):
        return f"<User(telegram_id={self.telegram_id}, username={self.username})>"


class SubscriptionPlan(Base):
    """Модель тарифного плана"""
    
    __tablename__ = "subscription_plans"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    price = Column(DECIMAL(10, 2), nullable=False)
    duration_days = Column(Integer, nullable=False)
    traffic_limit = Column(BigInteger, nullable=True)  # Лимит трафика в байтах (NULL = unlimited)
    max_devices = Column(Integer, default=1)
    is_active = Column(Boolean, default=True)
    sort_order = Column(Integer, default=0)
    marzban_config = Column(JSON, nullable=True)  # Конфигурация для Marzban
    currency = Column(String(10), default='RUB')  # Валюта
    created_at = Column(DateTime, default=func.now())
    
    # Relationships
    subscriptions = relationship("UserSubscription", back_populates="plan")
    
    # Indexes
    __table_args__ = (
        Index('idx_plan_active', 'is_active'),
        Index('idx_plan_sort', 'sort_order'),
    )
    
    def __repr__(self):
        return f"<SubscriptionPlan(name={self.name}, price={self.price})>"


class UserSubscription(Base):
    """Модель подписки пользователя"""
    
    __tablename__ = "user_subscriptions"
    
    id = Column(Integer, primary_key=True, autoincrement=True, index=True)
    user_id = Column(BigInteger, ForeignKey('users.telegram_id'), nullable=False)
    plan_id = Column(Integer, ForeignKey('subscription_plans.id'), nullable=False)
    marzban_user_id = Column(String(255), nullable=True, index=True)
    status = Column(SQLEnum(SubscriptionStatus), default=SubscriptionStatus.ACTIVE)
    start_date = Column(DateTime, default=func.now())
    end_date = Column(DateTime, nullable=True)
    traffic_limit = Column(BigInteger, nullable=True)  # Лимит трафика в байтах
    traffic_used = Column(BigInteger, default=0)  # Использованный трафик в байтах
    notes = Column(Text, nullable=True)  # Заметки
    transaction_id = Column(Integer, ForeignKey('transactions.id'), nullable=True)  # Связанная транзакция
    config_url = Column(Text, nullable=True)
    subscription_url = Column(Text, nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="subscriptions")
    plan = relationship("SubscriptionPlan", back_populates="subscriptions")
    transactions = relationship("Transaction", back_populates="subscription", foreign_keys="Transaction.subscription_id")
    
    # Indexes
    __table_args__ = (
        Index('idx_subscription_user_id', 'user_id'),
        Index('idx_subscription_status', 'status'),
        Index('idx_subscription_end_date', 'end_date'),
        Index('idx_subscription_marzban_id', 'marzban_user_id'),
    )
    
    def __repr__(self):
        return f"<UserSubscription(user_id={self.user_id}, status={self.status})>"


class Transaction(Base):
    """Модель транзакции"""
    
    __tablename__ = "transactions"
    
    id = Column(Integer, primary_key=True, autoincrement=True, index=True)
    user_id = Column(BigInteger, ForeignKey('users.telegram_id'), nullable=False)
    amount = Column(DECIMAL(10, 2), nullable=False)
    currency = Column(String(10), default='RUB')
    payment_system = Column(String(50), nullable=True)  # yukassa, cryptomus, telegram_stars
    payment_id = Column(String(255), nullable=True, index=True)
    status = Column(SQLEnum(TransactionStatus), default=TransactionStatus.PENDING)
    subscription_id = Column(BigInteger, ForeignKey('user_subscriptions.id'), nullable=True)
    promo_code_id = Column(Integer, ForeignKey('promo_codes.id'), nullable=True)
    payment_metadata = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="transactions", foreign_keys=[user_id])
    subscription = relationship("UserSubscription", back_populates="transactions", foreign_keys=[subscription_id])
    promo_code = relationship("PromoCode", back_populates="transactions")
    
    # Indexes
    __table_args__ = (
        Index('idx_transaction_user_id', 'user_id'),
        Index('idx_transaction_status', 'status'),
        Index('idx_transaction_payment_id', 'payment_id'),
        Index('idx_transaction_created', 'created_at'),
    )
    
    def __repr__(self):
        return f"<Transaction(id={self.id}, amount={self.amount}, status={self.status})>"


class PromoCode(Base):
    """Модель промокода"""
    
    __tablename__ = "promo_codes"
    
    id = Column(Integer, primary_key=True, index=True)
    code = Column(String(100), unique=True, nullable=False, index=True)
    discount_type = Column(String(20), nullable=False)  # percentage, fixed_amount, free_days
    discount_value = Column(DECIMAL(10, 2), nullable=False)
    max_uses = Column(Integer, nullable=True)  # NULL = unlimited
    current_uses = Column(Integer, default=0)
    valid_from = Column(DateTime, default=func.now())
    valid_until = Column(DateTime, nullable=True)
    is_active = Column(Boolean, default=True)
    created_by = Column(BigInteger, ForeignKey('users.telegram_id'), nullable=True)
    created_at = Column(DateTime, default=func.now())
    
    # Relationships
    creator = relationship("User", foreign_keys=[created_by])
    transactions = relationship("Transaction", back_populates="promo_code")
    
    # Indexes
    __table_args__ = (
        Index('idx_promo_code', 'code'),
        Index('idx_promo_active', 'is_active'),
        Index('idx_promo_valid_until', 'valid_until'),
    )
    
    def __repr__(self):
        return f"<PromoCode(code={self.code}, discount_type={self.discount_type})>"


class FAQItem(Base):
    """Модель FAQ элемента"""

    __tablename__ = "faq_items"

    id = Column(Integer, primary_key=True, index=True)
    question_ru = Column(Text, nullable=False)
    answer_ru = Column(Text, nullable=False)
    question_en = Column(Text, nullable=True)
    answer_en = Column(Text, nullable=True)
    category = Column(String(100), nullable=True)
    order_index = Column(Integer, default=0)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Indexes
    __table_args__ = (
        Index('idx_faq_active', 'is_active'),
        Index('idx_faq_category', 'category'),
        Index('idx_faq_order', 'order_index'),
    )

    def __repr__(self):
        return f"<FAQItem(id={self.id}, category={self.category})>"


class SupportTicket(Base):
    """Модель тикета поддержки"""

    __tablename__ = "support_tickets"

    id = Column(Integer, primary_key=True, autoincrement=True, index=True)
    user_id = Column(BigInteger, ForeignKey('users.telegram_id'), nullable=False)
    subject = Column(String(255), nullable=True)
    status = Column(String(50), default='open')  # open, in_progress, closed
    priority = Column(String(20), default='normal')  # low, normal, high, urgent
    assigned_to = Column(BigInteger, ForeignKey('users.telegram_id'), nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    closed_at = Column(DateTime, nullable=True)

    # Relationships
    user = relationship("User", back_populates="support_tickets", foreign_keys=[user_id])
    assigned_admin = relationship("User", foreign_keys=[assigned_to], post_update=True)
    messages = relationship("TicketMessage", back_populates="ticket", cascade="all, delete-orphan")

    # Indexes
    __table_args__ = (
        Index('idx_ticket_user_id', 'user_id'),
        Index('idx_ticket_status', 'status'),
        Index('idx_ticket_priority', 'priority'),
        Index('idx_ticket_created', 'created_at'),
    )

    def __repr__(self):
        return f"<SupportTicket(id={self.id}, status={self.status})>"


class TicketMessage(Base):
    """Модель сообщения в тикете"""

    __tablename__ = "ticket_messages"

    id = Column(Integer, primary_key=True, autoincrement=True, index=True)
    ticket_id = Column(BigInteger, ForeignKey('support_tickets.id'), nullable=False)
    sender_id = Column(BigInteger, ForeignKey('users.telegram_id'), nullable=False)
    message_text = Column(Text, nullable=False)
    is_from_admin = Column(Boolean, default=False)
    attachments = Column(JSON, nullable=True)  # Список файлов/изображений
    created_at = Column(DateTime, default=func.now())

    # Relationships
    ticket = relationship("SupportTicket", back_populates="messages")
    sender = relationship("User", foreign_keys=[sender_id])

    # Indexes
    __table_args__ = (
        Index('idx_message_ticket_id', 'ticket_id'),
        Index('idx_message_sender_id', 'sender_id'),
        Index('idx_message_created', 'created_at'),
    )

    def __repr__(self):
        return f"<TicketMessage(ticket_id={self.ticket_id}, sender_id={self.sender_id})>"


class SystemLog(Base):
    """Модель системного лога"""

    __tablename__ = "system_logs"

    id = Column(Integer, primary_key=True, autoincrement=True, index=True)
    level = Column(String(20), nullable=False)  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    message = Column(Text, nullable=False)
    module = Column(String(100), nullable=True)
    user_id = Column(BigInteger, nullable=True)
    log_metadata = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=func.now())

    # Indexes
    __table_args__ = (
        Index('idx_log_level', 'level'),
        Index('idx_log_module', 'module'),
        Index('idx_log_user_id', 'user_id'),
        Index('idx_log_created', 'created_at'),
    )

    def __repr__(self):
        return f"<SystemLog(level={self.level}, module={self.module})>"
