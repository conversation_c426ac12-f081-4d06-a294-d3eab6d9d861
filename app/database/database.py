"""
Конфигурация и подключение к базе данных
"""

from contextlib import asynccontextmanager
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import declarative_base
import structlog

from app.core.config import settings

logger = structlog.get_logger()

# Базовый класс для моделей
Base = declarative_base()

# Создание асинхронного движка базы данных
def create_engine():
    """Создание движка с учетом типа базы данных"""
    if settings.database_url.startswith('sqlite'):
        # Для SQLite не используем параметры пула
        return create_async_engine(
            settings.database_url,
            echo=settings.debug,
            future=True
        )
    else:
        # Для PostgreSQL используем параметры пула
        return create_async_engine(
            settings.database_url,
            pool_size=settings.db_pool_size,
            max_overflow=settings.db_max_overflow,
            pool_timeout=settings.db_pool_timeout,
            echo=settings.debug,
            future=True
        )

engine = create_engine()

# Фабрика сессий
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False
)


@asynccontextmanager
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Контекстный менеджер для получения сессии базы данных
    
    Yields:
        AsyncSession: Сессия базы данных
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            logger.error("Ошибка в сессии базы данных", error=str(e))
            await session.rollback()
            raise
        finally:
            await session.close()


async def init_db() -> None:
    """
    Инициализация базы данных
    Создание всех таблиц
    """
    try:
        # Импортируем все модели для создания таблиц
        from app.database.models import (
            User, SubscriptionPlan, UserSubscription, 
            Transaction, PromoCode, FAQItem, 
            SupportTicket, TicketMessage, SystemLog
        )
        
        async with engine.begin() as conn:
            # Создаем все таблицы
            await conn.run_sync(Base.metadata.create_all)
            
        logger.info("База данных успешно инициализирована")
        
    except Exception as e:
        logger.error("Ошибка инициализации базы данных", error=str(e))
        raise


async def close_db() -> None:
    """
    Закрытие соединения с базой данных
    """
    try:
        await engine.dispose()
        logger.info("Соединение с базой данных закрыто")
    except Exception as e:
        logger.error("Ошибка закрытия соединения с БД", error=str(e))


async def check_db_connection() -> bool:
    """
    Проверка соединения с базой данных
    
    Returns:
        bool: True если соединение успешно
    """
    try:
        async with get_db() as db:
            await db.execute("SELECT 1")
            return True
    except Exception as e:
        logger.error("Ошибка соединения с базой данных", error=str(e))
        return False
