"""
CRUD операции для работы с базой данных
"""

from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, and_, or_
from sqlalchemy.orm import selectinload
from datetime import datetime, timedelta, timezone
import structlog

from app.database.database import get_db
from app.database.models import (
    User, SubscriptionPlan, UserSubscription, 
    Transaction, PromoCode, FAQItem, 
    SupportTicket, TicketMessage, SystemLog
)
from app.core.utils import generate_referral_code
from app.core.exceptions import UserNotFoundException, DatabaseException

logger = structlog.get_logger()


async def _process_user_data(db: AsyncSession, user_data: Dict[str, Any]) -> User:
    """
    Внутренняя функция для обработки данных пользователя

    Args:
        db: Сессия базы данных
        user_data: Данные пользователя

    Returns:
        User: Объект пользователя
    """
    # Проверяем существование пользователя
    result = await db.execute(
        select(User).where(User.telegram_id == user_data['telegram_id'])
    )
    user = result.scalar_one_or_none()

    if not user:
        # Создаем нового пользователя
        user = User(**user_data)
        # Генерируем уникальный реферальный код
        user.referral_code = await _generate_unique_referral_code(db)

        db.add(user)
        await db.commit()
        await db.refresh(user)

        logger.info(
            "Создан новый пользователь",
            user_id=user.telegram_id,
            username=user.username
        )
    else:
        # Обновляем данные существующего пользователя
        for key, value in user_data.items():
            if hasattr(user, key) and value is not None:
                setattr(user, key, value)

        user.updated_at = datetime.now(timezone.utc)
        await db.commit()
        await db.refresh(user)

    return user


async def get_or_create_user(user_data: Dict[str, Any], db: AsyncSession = None) -> User:
    """
    Получение или создание пользователя

    Args:
        user_data: Данные пользователя
        db: Сессия базы данных (опционально)

    Returns:
        User: Объект пользователя

    Raises:
        DatabaseException: Ошибка базы данных
    """
    try:
        if db is None:
            async with get_db() as db:
                return await _process_user_data(db, user_data)
        else:
            return await _process_user_data(db, user_data)
            
    except Exception as e:
        logger.error("Ошибка при создании/получении пользователя", error=str(e))
        raise DatabaseException(f"Ошибка работы с пользователем: {str(e)}")


async def get_user_by_telegram_id(telegram_id: int, db: AsyncSession = None) -> Optional[User]:
    """
    Получение пользователя по Telegram ID

    Args:
        telegram_id: Telegram ID пользователя
        db: Сессия базы данных (опционально)

    Returns:
        Optional[User]: Пользователь или None
    """
    try:
        if db is None:
            async with get_db() as db:
                result = await db.execute(
                    select(User).where(User.telegram_id == telegram_id)
                )
                return result.scalar_one_or_none()
        else:
            result = await db.execute(
                select(User).where(User.telegram_id == telegram_id)
            )
            return result.scalar_one_or_none()

    except Exception as e:
        logger.error("Ошибка получения пользователя", error=str(e), user_id=telegram_id)
        return None


async def get_active_subscription_plans() -> List[SubscriptionPlan]:
    """
    Получение активных тарифных планов
    
    Returns:
        List[SubscriptionPlan]: Список активных планов
    """
    try:
        async with get_db() as db:
            result = await db.execute(
                select(SubscriptionPlan)
                .where(SubscriptionPlan.is_active == True)
                .order_by(SubscriptionPlan.sort_order, SubscriptionPlan.price)
            )
            return list(result.scalars().all())
            
    except Exception as e:
        logger.error("Ошибка получения тарифных планов", error=str(e))
        return []


async def get_subscription_plan_by_id(plan_id: int) -> Optional[SubscriptionPlan]:
    """
    Получение тарифного плана по ID
    
    Args:
        plan_id: ID плана
        
    Returns:
        Optional[SubscriptionPlan]: План или None
    """
    try:
        async with get_db() as db:
            result = await db.execute(
                select(SubscriptionPlan).where(SubscriptionPlan.id == plan_id)
            )
            return result.scalar_one_or_none()
            
    except Exception as e:
        logger.error("Ошибка получения плана", error=str(e), plan_id=plan_id)
        return None


async def create_user_subscription(
    user_id: int, 
    plan_id: int, 
    marzban_user_id: str = None
) -> UserSubscription:
    """
    Создание подписки пользователя
    
    Args:
        user_id: ID пользователя
        plan_id: ID плана
        marzban_user_id: ID пользователя в Marzban
        
    Returns:
        UserSubscription: Созданная подписка
        
    Raises:
        DatabaseException: Ошибка базы данных
    """
    try:
        async with get_db() as db:
            # Получаем план
            plan = await db.get(SubscriptionPlan, plan_id)
            if not plan:
                raise DatabaseException("Тарифный план не найден")
            
            # Создаем подписку
            subscription = UserSubscription(
                user_id=user_id,
                plan_id=plan_id,
                marzban_user_id=marzban_user_id,
                start_date=datetime.utcnow(),
                end_date=datetime.utcnow() + timedelta(days=plan.duration_days),
                status='active'
            )
            
            db.add(subscription)
            await db.commit()
            await db.refresh(subscription)
            
            logger.info(
                "Создана подписка",
                user_id=user_id,
                plan_id=plan_id,
                subscription_id=subscription.id
            )
            
            return subscription
            
    except Exception as e:
        logger.error("Ошибка создания подписки", error=str(e))
        raise DatabaseException(f"Ошибка создания подписки: {str(e)}")


async def get_user_subscriptions(user_id: int) -> List[UserSubscription]:
    """
    Получение подписок пользователя
    
    Args:
        user_id: ID пользователя
        
    Returns:
        List[UserSubscription]: Список подписок
    """
    try:
        async with get_db() as db:
            result = await db.execute(
                select(UserSubscription)
                .options(selectinload(UserSubscription.plan))
                .where(UserSubscription.user_id == user_id)
                .order_by(UserSubscription.created_at.desc())
            )
            return list(result.scalars().all())
            
    except Exception as e:
        logger.error("Ошибка получения подписок", error=str(e), user_id=user_id)
        return []


async def _generate_unique_referral_code(db: AsyncSession) -> str:
    """
    Генерация уникального реферального кода
    
    Args:
        db: Сессия базы данных
        
    Returns:
        str: Уникальный реферальный код
    """
    max_attempts = 10
    
    for _ in range(max_attempts):
        code = generate_referral_code()
        
        # Проверяем уникальность
        result = await db.execute(
            select(User).where(User.referral_code == code)
        )
        
        if not result.scalar_one_or_none():
            return code
    
    # Если не удалось сгенерировать уникальный код
    raise DatabaseException("Не удалось сгенерировать уникальный реферальный код")
