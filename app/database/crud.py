"""
CRUD операции для работы с базой данных
"""

from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, and_, or_
from sqlalchemy.orm import selectinload
from datetime import datetime, timedelta, timezone
import structlog

from app.database.database import get_db
from app.database.models import (
    User, SubscriptionPlan, UserSubscription, 
    Transaction, PromoCode, FAQItem, 
    SupportTicket, TicketMessage, SystemLog
)
from app.core.utils import generate_referral_code
from app.core.exceptions import UserNotFoundException, DatabaseException

logger = structlog.get_logger()


async def _process_user_data(db: AsyncSession, user_data: Dict[str, Any]) -> User:
    """
    Внутренняя функция для обработки данных пользователя

    Args:
        db: Сессия базы данных
        user_data: Данные пользователя

    Returns:
        User: Объект пользователя
    """
    # Проверяем существование пользователя
    result = await db.execute(
        select(User).where(User.telegram_id == user_data['telegram_id'])
    )
    user = result.scalar_one_or_none()

    if not user:
        # Создаем нового пользователя
        user = User(**user_data)
        # Генерируем уникальный реферальный код
        user.referral_code = await _generate_unique_referral_code(db)

        db.add(user)
        await db.commit()
        await db.refresh(user)

        logger.info(
            "Создан новый пользователь",
            user_id=user.telegram_id,
            username=user.username
        )
    else:
        # Обновляем данные существующего пользователя
        for key, value in user_data.items():
            if hasattr(user, key) and value is not None:
                setattr(user, key, value)

        user.updated_at = datetime.now(timezone.utc)
        await db.commit()
        await db.refresh(user)

    return user


async def get_or_create_user(user_data: Dict[str, Any], db: AsyncSession = None) -> User:
    """
    Получение или создание пользователя

    Args:
        user_data: Данные пользователя
        db: Сессия базы данных (опционально)

    Returns:
        User: Объект пользователя

    Raises:
        DatabaseException: Ошибка базы данных
    """
    try:
        if db is None:
            async with get_db() as db:
                return await _process_user_data(db, user_data)
        else:
            return await _process_user_data(db, user_data)
            
    except Exception as e:
        logger.error("Ошибка при создании/получении пользователя", error=str(e))
        raise DatabaseException(f"Ошибка работы с пользователем: {str(e)}")


async def get_user_by_telegram_id(telegram_id: int, db: AsyncSession = None) -> Optional[User]:
    """
    Получение пользователя по Telegram ID

    Args:
        telegram_id: Telegram ID пользователя
        db: Сессия базы данных (опционально)

    Returns:
        Optional[User]: Пользователь или None
    """
    try:
        if db is None:
            async with get_db() as db:
                result = await db.execute(
                    select(User).where(User.telegram_id == telegram_id)
                )
                return result.scalar_one_or_none()
        else:
            result = await db.execute(
                select(User).where(User.telegram_id == telegram_id)
            )
            return result.scalar_one_or_none()

    except Exception as e:
        logger.error("Ошибка получения пользователя", error=str(e), user_id=telegram_id)
        return None


async def get_active_subscription_plans(db: AsyncSession = None) -> List[SubscriptionPlan]:
    """
    Получение активных тарифных планов
    
    Returns:
        List[SubscriptionPlan]: Список активных планов
    """
    try:
        if db is None:
            async with get_db() as db:
                result = await db.execute(
                    select(SubscriptionPlan)
                    .where(SubscriptionPlan.is_active == True)
                    .order_by(SubscriptionPlan.sort_order, SubscriptionPlan.price)
                )
                return list(result.scalars().all())
        else:
            result = await db.execute(
                select(SubscriptionPlan)
                .where(SubscriptionPlan.is_active == True)
                .order_by(SubscriptionPlan.sort_order, SubscriptionPlan.price)
            )
            return list(result.scalars().all())

    except Exception as e:
        logger.error("Ошибка получения тарифных планов", error=str(e))
        return []


async def get_subscription_plan_by_id(plan_id: int, db: AsyncSession = None) -> Optional[SubscriptionPlan]:
    """
    Получение тарифного плана по ID
    
    Args:
        plan_id: ID плана
        
    Returns:
        Optional[SubscriptionPlan]: План или None
    """
    try:
        if db is None:
            async with get_db() as db:
                result = await db.execute(
                    select(SubscriptionPlan).where(SubscriptionPlan.id == plan_id)
                )
                return result.scalar_one_or_none()
        else:
            result = await db.execute(
                select(SubscriptionPlan).where(SubscriptionPlan.id == plan_id)
            )
            return result.scalar_one_or_none()

    except Exception as e:
        logger.error("Ошибка получения плана", error=str(e), plan_id=plan_id)
        return None


async def create_user_subscription(
    user_id: int,
    plan_id: int,
    marzban_user_id: str = None,
    db: AsyncSession = None
) -> UserSubscription:
    """
    Создание подписки пользователя
    
    Args:
        user_id: ID пользователя
        plan_id: ID плана
        marzban_user_id: ID пользователя в Marzban
        
    Returns:
        UserSubscription: Созданная подписка
        
    Raises:
        DatabaseException: Ошибка базы данных
    """
    async def _create_subscription(session: AsyncSession):
        # Получаем план
        plan = await session.get(SubscriptionPlan, plan_id)
        if not plan:
            raise DatabaseException("Тарифный план не найден")

        # Создаем подписку
        subscription = UserSubscription(
            user_id=user_id,
            plan_id=plan_id,
            marzban_user_id=marzban_user_id,
            start_date=datetime.now(timezone.utc),
            end_date=datetime.now(timezone.utc) + timedelta(days=plan.duration_days),
            status='active'
        )

        session.add(subscription)
        await session.commit()
        await session.refresh(subscription)

        logger.info(
            "Создана подписка",
            user_id=user_id,
            plan_id=plan_id,
            subscription_id=subscription.id
        )

        return subscription

    try:
        if db is None:
            async with get_db() as db:
                return await _create_subscription(db)
        else:
            return await _create_subscription(db)

    except Exception as e:
        logger.error("Ошибка создания подписки", error=str(e))
        raise DatabaseException(f"Ошибка создания подписки: {str(e)}")


async def get_user_subscriptions(user_id: int, db: AsyncSession = None) -> List[UserSubscription]:
    """
    Получение подписок пользователя
    
    Args:
        user_id: ID пользователя
        
    Returns:
        List[UserSubscription]: Список подписок
    """
    try:
        if db is None:
            async with get_db() as db:
                result = await db.execute(
                    select(UserSubscription)
                    .where(UserSubscription.user_id == user_id)
                    .order_by(UserSubscription.created_at.desc())
                )
                return list(result.scalars().all())
        else:
            result = await db.execute(
                select(UserSubscription)
                .options(selectinload(UserSubscription.plan))
                .where(UserSubscription.user_id == user_id)
                .order_by(UserSubscription.created_at.desc())
            )
            return list(result.scalars().all())

    except Exception as e:
        logger.error("Ошибка получения подписок", error=str(e), user_id=user_id)
        return []


async def _generate_unique_referral_code(db: AsyncSession) -> str:
    """
    Генерация уникального реферального кода
    
    Args:
        db: Сессия базы данных
        
    Returns:
        str: Уникальный реферальный код
    """
    max_attempts = 10
    
    for _ in range(max_attempts):
        code = generate_referral_code()
        
        # Проверяем уникальность
        result = await db.execute(
            select(User).where(User.referral_code == code)
        )
        
        if not result.scalar_one_or_none():
            return code
    
    # Если не удалось сгенерировать уникальный код
    raise DatabaseException("Не удалось сгенерировать уникальный реферальный код")


async def create_user_subscription(db: AsyncSession, subscription_data: dict) -> UserSubscription:
    """
    Создание подписки пользователя с произвольными данными

    Args:
        db: Сессия базы данных
        subscription_data: Данные подписки

    Returns:
        UserSubscription: Созданная подписка
    """
    try:
        subscription = UserSubscription(**subscription_data)
        db.add(subscription)
        await db.flush()  # Получаем ID без коммита
        await db.refresh(subscription)
        return subscription
    except Exception as e:
        logger.error("Ошибка создания подписки", error=str(e))
        raise DatabaseException(f"Ошибка создания подписки: {str(e)}")


async def update_user_subscription(
    db: AsyncSession,
    subscription_id: int,
    update_data: dict
) -> Optional[UserSubscription]:
    """
    Обновление подписки пользователя

    Args:
        db: Сессия базы данных
        subscription_id: ID подписки
        update_data: Данные для обновления

    Returns:
        UserSubscription: Обновленная подписка или None
    """
    try:
        subscription = await db.get(UserSubscription, subscription_id)
        if not subscription:
            return None

        for key, value in update_data.items():
            if hasattr(subscription, key):
                setattr(subscription, key, value)

        await db.flush()
        await db.refresh(subscription)
        return subscription
    except Exception as e:
        logger.error("Ошибка обновления подписки", error=str(e))
        raise DatabaseException(f"Ошибка обновления подписки: {str(e)}")


# ==================== CRUD для транзакций ====================

async def create_transaction(db: AsyncSession, transaction_data: dict) -> Transaction:
    """
    Создание транзакции

    Args:
        db: Сессия базы данных
        transaction_data: Данные транзакции

    Returns:
        Transaction: Созданная транзакция
    """
    try:
        transaction = Transaction(**transaction_data)
        db.add(transaction)
        await db.flush()
        await db.refresh(transaction)

        logger.info(
            "Создана транзакция",
            transaction_id=transaction.id,
            payment_id=transaction.payment_id,
            amount=float(transaction.amount),
            user_id=transaction.user_id
        )

        return transaction
    except Exception as e:
        logger.error("Ошибка создания транзакции", error=str(e))
        raise DatabaseException(f"Ошибка создания транзакции: {str(e)}")


async def get_transaction_by_id(db: AsyncSession, transaction_id: int) -> Optional[Transaction]:
    """
    Получение транзакции по ID

    Args:
        db: Сессия базы данных
        transaction_id: ID транзакции

    Returns:
        Optional[Transaction]: Транзакция или None
    """
    try:
        result = await db.execute(
            select(Transaction).where(Transaction.id == transaction_id)
        )
        return result.scalar_one_or_none()
    except Exception as e:
        logger.error("Ошибка получения транзакции", error=str(e), transaction_id=transaction_id)
        return None


async def get_transaction_by_payment_id(db: AsyncSession, payment_id: str) -> Optional[Transaction]:
    """
    Получение транзакции по payment_id

    Args:
        db: Сессия базы данных
        payment_id: ID платежа

    Returns:
        Optional[Transaction]: Транзакция или None
    """
    try:
        result = await db.execute(
            select(Transaction).where(Transaction.payment_id == payment_id)
        )
        return result.scalar_one_or_none()
    except Exception as e:
        logger.error("Ошибка получения транзакции по payment_id", error=str(e), payment_id=payment_id)
        return None


async def update_transaction_by_payment_id(
    db: AsyncSession,
    payment_id: str,
    update_data: dict
) -> Optional[Transaction]:
    """
    Обновление транзакции по payment_id

    Args:
        db: Сессия базы данных
        payment_id: ID платежа
        update_data: Данные для обновления

    Returns:
        Optional[Transaction]: Обновленная транзакция или None
    """
    try:
        transaction = await get_transaction_by_payment_id(db, payment_id)
        if not transaction:
            return None

        for key, value in update_data.items():
            if hasattr(transaction, key):
                setattr(transaction, key, value)

        transaction.updated_at = datetime.now(timezone.utc)
        await db.flush()
        await db.refresh(transaction)

        logger.info(
            "Обновлена транзакция",
            transaction_id=transaction.id,
            payment_id=payment_id,
            update_data=update_data
        )

        return transaction
    except Exception as e:
        logger.error("Ошибка обновления транзакции", error=str(e), payment_id=payment_id)
        raise DatabaseException(f"Ошибка обновления транзакции: {str(e)}")


async def get_user_transactions(
    db: AsyncSession,
    user_id: int,
    limit: int = 50,
    offset: int = 0
) -> List[Transaction]:
    """
    Получение транзакций пользователя

    Args:
        db: Сессия базы данных
        user_id: ID пользователя
        limit: Лимит записей
        offset: Смещение

    Returns:
        List[Transaction]: Список транзакций
    """
    try:
        result = await db.execute(
            select(Transaction)
            .where(Transaction.user_id == user_id)
            .order_by(Transaction.created_at.desc())
            .limit(limit)
            .offset(offset)
        )
        return list(result.scalars().all())
    except Exception as e:
        logger.error("Ошибка получения транзакций пользователя", error=str(e), user_id=user_id)
        return []


async def get_transactions_by_status(
    db: AsyncSession,
    status: str,
    limit: int = 100
) -> List[Transaction]:
    """
    Получение транзакций по статусу

    Args:
        db: Сессия базы данных
        status: Статус транзакций
        limit: Лимит записей

    Returns:
        List[Transaction]: Список транзакций
    """
    try:
        result = await db.execute(
            select(Transaction)
            .where(Transaction.status == status)
            .order_by(Transaction.created_at.desc())
            .limit(limit)
        )
        return list(result.scalars().all())
    except Exception as e:
        logger.error("Ошибка получения транзакций по статусу", error=str(e), status=status)
        return []


async def get_pending_transactions(db: AsyncSession, older_than_minutes: int = 30) -> List[Transaction]:
    """
    Получение зависших транзакций

    Args:
        db: Сессия базы данных
        older_than_minutes: Возраст транзакций в минутах

    Returns:
        List[Transaction]: Список зависших транзакций
    """
    try:
        cutoff_time = datetime.now(timezone.utc) - timedelta(minutes=older_than_minutes)

        result = await db.execute(
            select(Transaction)
            .where(
                and_(
                    Transaction.status == 'pending',
                    Transaction.created_at < cutoff_time
                )
            )
            .order_by(Transaction.created_at.asc())
        )
        return list(result.scalars().all())
    except Exception as e:
        logger.error("Ошибка получения зависших транзакций", error=str(e))
        return []
