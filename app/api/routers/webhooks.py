"""
FastAPI роутеры для обработки webhook'ов от платежных систем
"""

from typing import Dict, Any
import structlog
from fastapi import APIRouter, Request, HTTPException, Depends, BackgroundTasks
from fastapi.responses import JSONResponse

from app.database.database import get_async_session, AsyncSession
from app.services.payment_service import PaymentService, PaymentProvider
from app.services.providers import YooKassaProvider, CryptomusProvider
from app.services.subscription_service import SubscriptionService
from app.services.notification_service import NotificationService
from app.core.config import settings

logger = structlog.get_logger()
webhooks_router = APIRouter(prefix="/webhooks", tags=["webhooks"])


async def get_payment_service(session: AsyncSession = Depends(get_async_session)) -> PaymentService:
    """Dependency для получения сервиса платежей"""
    payment_service = PaymentService(session)
    
    # Регистрируем провайдеров
    if settings.YOOKASSA_SHOP_ID and settings.YOOKASSA_SECRET_KEY:
        yookassa_provider = YooKassaProvider(
            shop_id=settings.YOOKASSA_SHOP_ID,
            secret_key=settings.YOOKASSA_SECRET_KEY
        )
        payment_service.register_provider(PaymentProvider.YOOKASSA, yookassa_provider)
    
    if settings.CRYPTOMUS_MERCHANT_ID and settings.CRYPTOMUS_API_KEY:
        cryptomus_provider = CryptomusProvider(
            merchant_id=settings.CRYPTOMUS_MERCHANT_ID,
            api_key=settings.CRYPTOMUS_API_KEY
        )
        payment_service.register_provider(PaymentProvider.CRYPTOMUS, cryptomus_provider)
    
    return payment_service


async def process_successful_payment_background(
    payment_result,
    session: AsyncSession
):
    """Фоновая обработка успешного платежа"""
    try:
        # Получаем метаданные платежа
        metadata = payment_result.metadata
        plan_id = metadata.get("plan_id")
        telegram_user_id = metadata.get("telegram_user_id")
        
        if not plan_id or not telegram_user_id:
            logger.warning(
                "Недостаточно данных для активации подписки",
                payment_id=payment_result.payment_id,
                metadata=metadata
            )
            return
        
        # Активируем подписку
        subscription_service = SubscriptionService(session)
        subscription = await subscription_service.create_subscription(
            user_id=int(telegram_user_id),
            plan_id=int(plan_id)
        )
        
        # Отправляем уведомление
        notification_service = NotificationService()
        await notification_service.send_subscription_activated(
            user_id=int(telegram_user_id),
            subscription=subscription
        )
        
        logger.info(
            "Подписка активирована после оплаты",
            payment_id=payment_result.payment_id,
            subscription_id=subscription.id,
            user_id=telegram_user_id
        )
        
    except Exception as e:
        logger.error(
            "Ошибка активации подписки после оплаты",
            error=str(e),
            payment_id=payment_result.payment_id
        )


@webhooks_router.post("/yookassa")
async def yookassa_webhook(
    request: Request,
    background_tasks: BackgroundTasks,
    payment_service: PaymentService = Depends(get_payment_service)
):
    """
    Webhook для обработки уведомлений от ЮKassa
    """
    try:
        # Получаем заголовки и тело запроса
        headers = dict(request.headers)
        body = await request.body()
        body_json = await request.json()
        
        logger.info("Получен webhook ЮKassa", headers=headers)
        
        # Обрабатываем webhook
        result = await payment_service.process_webhook(
            provider=PaymentProvider.YOOKASSA,
            headers=headers,
            body=body_json
        )
        
        if result:
            # Если платеж успешен, активируем подписку в фоне
            if result.status.value == "succeeded":
                background_tasks.add_task(
                    process_successful_payment_background,
                    result,
                    payment_service.db_session
                )
            
            logger.info(
                "Webhook ЮKassa обработан",
                payment_id=result.payment_id,
                status=result.status.value
            )
            
            return JSONResponse(
                status_code=200,
                content={"status": "ok", "payment_id": result.payment_id}
            )
        else:
            logger.warning("Webhook ЮKassa не обработан")
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": "Invalid webhook"}
            )
            
    except Exception as e:
        logger.error("Ошибка обработки webhook ЮKassa", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@webhooks_router.post("/cryptomus")
async def cryptomus_webhook(
    request: Request,
    background_tasks: BackgroundTasks,
    payment_service: PaymentService = Depends(get_payment_service)
):
    """
    Webhook для обработки уведомлений от Cryptomus
    """
    try:
        # Получаем заголовки и тело запроса
        headers = dict(request.headers)
        body = await request.body()
        body_json = await request.json()
        
        logger.info("Получен webhook Cryptomus", headers=headers)
        
        # Обрабатываем webhook
        result = await payment_service.process_webhook(
            provider=PaymentProvider.CRYPTOMUS,
            headers=headers,
            body=body_json
        )
        
        if result:
            # Если платеж успешен, активируем подписку в фоне
            if result.status.value == "succeeded":
                background_tasks.add_task(
                    process_successful_payment_background,
                    result,
                    payment_service.db_session
                )
            
            logger.info(
                "Webhook Cryptomus обработан",
                payment_id=result.payment_id,
                status=result.status.value
            )
            
            return JSONResponse(
                status_code=200,
                content={"status": "ok", "payment_id": result.payment_id}
            )
        else:
            logger.warning("Webhook Cryptomus не обработан")
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": "Invalid webhook"}
            )
            
    except Exception as e:
        logger.error("Ошибка обработки webhook Cryptomus", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@webhooks_router.get("/health")
async def health_check():
    """
    Проверка здоровья webhook endpoints
    """
    return JSONResponse(
        status_code=200,
        content={
            "status": "healthy",
            "service": "payment-webhooks",
            "providers": ["yookassa", "cryptomus"]
        }
    )


@webhooks_router.post("/test")
async def test_webhook(
    request: Request,
    payment_service: PaymentService = Depends(get_payment_service)
):
    """
    Тестовый endpoint для проверки webhook'ов
    """
    try:
        body = await request.json()
        provider = body.get("provider", "yookassa")
        
        logger.info("Тестовый webhook", provider=provider, body=body)
        
        # Создаем тестовый результат
        from app.services.payment_service import PaymentResult, PaymentStatus
        from decimal import Decimal
        
        test_result = PaymentResult(
            payment_id="test_payment_123",
            status=PaymentStatus.SUCCEEDED,
            amount=Decimal("100.00"),
            currency="RUB",
            provider=PaymentProvider.YOOKASSA,
            metadata={"test": True}
        )
        
        return JSONResponse(
            status_code=200,
            content={
                "status": "test_ok",
                "result": test_result.to_dict()
            }
        )
        
    except Exception as e:
        logger.error("Ошибка тестового webhook", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@webhooks_router.get("/status/{payment_id}")
async def get_payment_status(
    payment_id: str,
    provider: str = "yookassa",
    payment_service: PaymentService = Depends(get_payment_service)
):
    """
    Получает статус платежа по ID
    """
    try:
        provider_enum = PaymentProvider(provider)
        result = await payment_service.get_payment_status(payment_id, provider_enum)
        
        return JSONResponse(
            status_code=200,
            content={
                "status": "ok",
                "payment": result.to_dict()
            }
        )
        
    except ValueError:
        raise HTTPException(status_code=400, detail=f"Unsupported provider: {provider}")
    except Exception as e:
        logger.error("Ошибка получения статуса платежа", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))
