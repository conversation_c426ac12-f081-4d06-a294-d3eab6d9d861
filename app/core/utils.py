"""
Утилиты и вспомогательные функции
"""

import secrets
import string
import hashlib
from datetime import datetime, timezone
from typing import Optional, Any, Dict
from cryptography.fernet import Fernet
import structlog

from app.core.config import settings

logger = structlog.get_logger()


def generate_referral_code(length: int = 8) -> str:
    """
    Генерация уникального реферального кода
    
    Args:
        length: Длина кода
        
    Returns:
        Реферальный код
    """
    alphabet = string.ascii_uppercase + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(length))


def generate_random_string(length: int = 32) -> str:
    """
    Генерация случайной строки
    
    Args:
        length: Длина строки
        
    Returns:
        Случайная строка
    """
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(length))


def hash_password(password: str) -> str:
    """
    Хеширование пароля
    
    Args:
        password: Пароль для хеширования
        
    Returns:
        Хешированный пароль
    """
    return hashlib.sha256(password.encode()).hexdigest()


def encrypt_data(data: str) -> str:
    """
    Шифрование данных
    
    Args:
        data: Данные для шифрования
        
    Returns:
        Зашифрованные данные
    """
    try:
        fernet = Fernet(settings.encryption_key.encode())
        encrypted_data = fernet.encrypt(data.encode())
        return encrypted_data.decode()
    except Exception as e:
        logger.error("Ошибка шифрования данных", error=str(e))
        raise


def decrypt_data(encrypted_data: str) -> str:
    """
    Расшифровка данных
    
    Args:
        encrypted_data: Зашифрованные данные
        
    Returns:
        Расшифрованные данные
    """
    try:
        fernet = Fernet(settings.encryption_key.encode())
        decrypted_data = fernet.decrypt(encrypted_data.encode())
        return decrypted_data.decode()
    except Exception as e:
        logger.error("Ошибка расшифровки данных", error=str(e))
        raise


def get_current_timestamp() -> datetime:
    """
    Получение текущего времени в UTC
    
    Returns:
        Текущее время
    """
    return datetime.now(timezone.utc)


def format_bytes(bytes_value: int) -> str:
    """
    Форматирование байтов в читаемый вид
    
    Args:
        bytes_value: Количество байтов
        
    Returns:
        Отформатированная строка
    """
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if bytes_value < 1024.0:
            return f"{bytes_value:.1f} {unit}"
        bytes_value /= 1024.0
    return f"{bytes_value:.1f} PB"


def validate_telegram_id(telegram_id: Any) -> bool:
    """
    Валидация Telegram ID
    
    Args:
        telegram_id: ID для валидации
        
    Returns:
        True если ID валидный
    """
    try:
        id_int = int(telegram_id)
        return 0 < id_int < 10**12  # Telegram ID не может быть больше 12 цифр
    except (ValueError, TypeError):
        return False


def sanitize_username(username: str) -> str:
    """
    Очистка имени пользователя для использования в Marzban

    Args:
        username: Исходное имя пользователя

    Returns:
        Очищенное имя пользователя
    """
    # Удаляем недопустимые символы и приводим к нижнему регистру
    allowed_chars = string.ascii_lowercase + string.digits + '_-.'
    cleaned = ''.join(c for c in username.lower() if c in allowed_chars)

    # Убеждаемся что имя не пустое
    if not cleaned:
        cleaned = f"user_{generate_random_string(8).lower()}"

    return cleaned


def create_marzban_username(telegram_id: int, suffix: Optional[str] = None) -> str:
    """
    Создание имени пользователя для Marzban
    
    Args:
        telegram_id: Telegram ID пользователя
        suffix: Дополнительный суффикс
        
    Returns:
        Имя пользователя для Marzban
    """
    base_name = f"unveil_{telegram_id}"
    if suffix:
        base_name += f"_{suffix}"
    
    return base_name[:50]  # Ограничиваем длину
