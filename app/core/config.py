"""
Конфигурация приложения
"""

from typing import List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Настройки приложения"""
    
    # Database
    database_url: str = Field(..., env="DATABASE_URL")
    redis_url: str = Field(..., env="REDIS_URL")
    
    # Telegram Bots
    main_bot_token: str = Field(..., env="MAIN_BOT_TOKEN")
    monitor_bot_token: str = Field(..., env="MONITOR_BOT_TOKEN")
    support_bot_token: str = Field(..., env="SUPPORT_BOT_TOKEN")
    admin_telegram_ids: str = Field(..., env="ADMIN_TELEGRAM_IDS")
    
    # Marzban API
    marzban_api_url: str = Field(..., env="MARZBAN_API_URL")
    marzban_username: str = Field(..., env="MARZBAN_USERNAME")
    marzban_password: str = Field(..., env="MARZBAN_PASSWORD")
    
    # Payment Systems
    yukassa_shop_id: Optional[str] = Field(None, env="YUKASSA_SHOP_ID")
    yukassa_secret_key: Optional[str] = Field(None, env="YUKASSA_SECRET_KEY")
    cryptomus_merchant_id: Optional[str] = Field(None, env="CRYPTOMUS_MERCHANT_ID")
    cryptomus_api_key: Optional[str] = Field(None, env="CRYPTOMUS_API_KEY")
    
    # Security
    secret_key: str = Field(..., env="SECRET_KEY")
    encryption_key: str = Field(..., env="ENCRYPTION_KEY")
    
    # Web
    web_host: str = Field("0.0.0.0", env="WEB_HOST")
    web_port: int = Field(8000, env="WEB_PORT")
    domain: str = Field(..., env="DOMAIN")
    
    # Environment
    environment: str = Field("development", env="ENVIRONMENT")
    debug: bool = Field(True, env="DEBUG")
    log_level: str = Field("INFO", env="LOG_LEVEL")
    
    # Database connection pool
    db_pool_size: int = Field(20, env="DB_POOL_SIZE")
    db_max_overflow: int = Field(30, env="DB_MAX_OVERFLOW")
    db_pool_timeout: int = Field(30, env="DB_POOL_TIMEOUT")
    
    # Redis settings
    redis_pool_size: int = Field(10, env="REDIS_POOL_SIZE")
    redis_timeout: int = Field(5, env="REDIS_TIMEOUT")

    # Payment systems settings
    # ЮKassa
    yookassa_shop_id: Optional[str] = Field(None, env="YOOKASSA_SHOP_ID")
    yookassa_secret_key: Optional[str] = Field(None, env="YOOKASSA_SECRET_KEY")
    yookassa_return_url: str = Field("https://example.com/payment/return", env="YOOKASSA_RETURN_URL")

    # Cryptomus
    cryptomus_merchant_id: Optional[str] = Field(None, env="CRYPTOMUS_MERCHANT_ID")
    cryptomus_api_key: Optional[str] = Field(None, env="CRYPTOMUS_API_KEY")
    cryptomus_return_url: str = Field("https://example.com/payment/return", env="CRYPTOMUS_RETURN_URL")
    cryptomus_webhook_url: str = Field("https://example.com/webhooks/cryptomus", env="CRYPTOMUS_WEBHOOK_URL")

    # Payment general settings
    payment_return_url: str = Field("https://example.com/payment/success", env="PAYMENT_RETURN_URL")
    payment_webhook_secret: Optional[str] = Field(None, env="PAYMENT_WEBHOOK_SECRET")

    # Currency conversion rates (примерные курсы, в продакшене получать из API)
    usd_to_rub_rate: float = Field(90.0, env="USD_TO_RUB_RATE")
    usdt_to_rub_rate: float = Field(90.0, env="USDT_TO_RUB_RATE")
    stars_to_rub_rate: float = Field(2.0, env="STARS_TO_RUB_RATE")

    # Админ-панель настройки
    admin_secret_key: str = Field(default="admin-secret-key-change-in-production", env="ADMIN_SECRET_KEY")
    admin_access_token_expire_minutes: int = Field(default=480, env="ADMIN_ACCESS_TOKEN_EXPIRE_MINUTES")  # 8 часов
    admin_refresh_token_expire_days: int = Field(default=30, env="ADMIN_REFRESH_TOKEN_EXPIRE_DAYS")
    admin_site_url: str = Field(default="http://localhost:8000", env="ADMIN_SITE_URL")
    admin_site_title: str = Field(default="Unveil VPN Admin", env="ADMIN_SITE_TITLE")
    admin_language: str = Field(default="ru", env="ADMIN_LANGUAGE")

    @property
    def admin_ids(self) -> List[int]:
        """Получение списка ID администраторов"""
        return [int(id_.strip()) for id_ in self.admin_telegram_ids.split(",") if id_.strip()]
    
    @property
    def is_production(self) -> bool:
        """Проверка продакшн окружения"""
        return self.environment.lower() == "production"

    @property
    def yookassa_enabled(self) -> bool:
        """Проверка доступности ЮKassa"""
        return bool(self.yookassa_shop_id and self.yookassa_secret_key)

    @property
    def cryptomus_enabled(self) -> bool:
        """Проверка доступности Cryptomus"""
        return bool(self.cryptomus_merchant_id and self.cryptomus_api_key)

    @property
    def available_payment_providers(self) -> List[str]:
        """Список доступных платежных провайдеров"""
        providers = ["telegram_stars"]  # Telegram Stars всегда доступен

        if self.yookassa_enabled:
            providers.append("yookassa")

        if self.cryptomus_enabled:
            providers.append("cryptomus")

        return providers
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Глобальный экземпляр настроек
settings = Settings()
