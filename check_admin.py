#!/usr/bin/env python3
"""
Простой скрипт для проверки администраторов
"""

import asyncio
from app.database.database import AsyncSessionLocal
from app.admin.models import AdminUser
from sqlalchemy import select


async def check_admins():
    """Проверяет администраторов в БД"""
    
    async with AsyncSessionLocal() as db:
        try:
            # Получаем всех администраторов
            result = await db.execute(select(AdminUser))
            admins = result.scalars().all()
            
            if not admins:
                print("📭 Администраторы не найдены")
                return
            
            print(f"👥 Найдено администраторов: {len(admins)}")
            print("=" * 50)
            
            for admin in admins:
                status = "🟢 Активен" if admin.is_active else "🔴 Неактивен"
                super_badge = " 👑" if admin.is_superuser else ""
                
                print(f"ID: {admin.id}")
                print(f"Username: {admin.username}{super_badge}")
                print(f"Email: {admin.email}")
                print(f"Роль: {admin.role}")
                print(f"Статус: {status}")
                print(f"Создан: {admin.created_at}")
                if admin.last_login:
                    print(f"Последний вход: {admin.last_login}")
                print("-" * 30)
                
        except Exception as e:
            print(f"❌ Ошибка: {e}")


if __name__ == "__main__":
    asyncio.run(check_admins())
