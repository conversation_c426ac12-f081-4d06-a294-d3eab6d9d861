"""
Тесты для webhook endpoints
"""

import pytest
import json
from unittest.mock import AsyncMock, patch
from fastapi.testclient import TestClient
from httpx import AsyncClient

from app.main import app
from app.services.payment_service import PaymentResult, PaymentStatus, PaymentProvider
from decimal import Decimal


@pytest.mark.asyncio
class TestWebhookEndpoints:
    """Тесты для webhook endpoints"""
    
    @pytest.fixture
    def client(self):
        """Фикстура для создания тестового клиента"""
        return TestClient(app)
    
    async def test_yookassa_webhook_success(self, client):
        """Тест успешной обработки webhook от ЮKassa"""
        webhook_data = {
            "event": "payment.succeeded",
            "object": {
                "id": "test_payment_123",
                "status": "succeeded",
                "amount": {"value": "100.00", "currency": "RUB"},
                "metadata": {
                    "plan_id": "1",
                    "telegram_user_id": "123456789"
                }
            }
        }
        
        with patch('app.api.routers.webhooks.get_payment_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_result = PaymentResult(
                payment_id="test_payment_123",
                status=PaymentStatus.SUCCEEDED,
                amount=Decimal("100.00"),
                currency="RUB",
                provider=PaymentProvider.YOOKASSA,
                metadata={
                    "plan_id": "1",
                    "telegram_user_id": "123456789"
                }
            )
            mock_service.process_webhook.return_value = mock_result
            mock_get_service.return_value = mock_service
            
            response = client.post(
                "/webhooks/yookassa",
                json=webhook_data,
                headers={"Content-Type": "application/json"}
            )
            
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["status"] == "ok"
            assert response_data["payment_id"] == "test_payment_123"
    
    async def test_yookassa_webhook_invalid(self, client):
        """Тест обработки невалидного webhook от ЮKassa"""
        webhook_data = {"invalid": "data"}
        
        with patch('app.api.routers.webhooks.get_payment_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_service.process_webhook.return_value = None  # Невалидный webhook
            mock_get_service.return_value = mock_service
            
            response = client.post(
                "/webhooks/yookassa",
                json=webhook_data,
                headers={"Content-Type": "application/json"}
            )
            
            assert response.status_code == 400
            response_data = response.json()
            assert response_data["status"] == "error"
    
    async def test_cryptomus_webhook_success(self, client):
        """Тест успешной обработки webhook от Cryptomus"""
        webhook_data = {
            "uuid": "test_payment_123",
            "status": "paid",
            "amount": "10.00",
            "currency": "USDT"
        }
        
        with patch('app.api.routers.webhooks.get_payment_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_result = PaymentResult(
                payment_id="test_payment_123",
                status=PaymentStatus.SUCCEEDED,
                amount=Decimal("10.00"),
                currency="USDT",
                provider=PaymentProvider.CRYPTOMUS
            )
            mock_service.process_webhook.return_value = mock_result
            mock_get_service.return_value = mock_service
            
            response = client.post(
                "/webhooks/cryptomus",
                json=webhook_data,
                headers={"Content-Type": "application/json"}
            )
            
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["status"] == "ok"
            assert response_data["payment_id"] == "test_payment_123"
    
    async def test_health_check(self, client):
        """Тест проверки здоровья webhook endpoints"""
        response = client.get("/webhooks/health")
        
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["status"] == "healthy"
        assert response_data["service"] == "payment-webhooks"
        assert "yookassa" in response_data["providers"]
        assert "cryptomus" in response_data["providers"]
    
    async def test_test_webhook(self, client):
        """Тест тестового webhook endpoint"""
        test_data = {
            "provider": "yookassa",
            "test": True
        }
        
        with patch('app.api.routers.webhooks.get_payment_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            response = client.post(
                "/webhooks/test",
                json=test_data,
                headers={"Content-Type": "application/json"}
            )
            
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["status"] == "test_ok"
            assert "result" in response_data
    
    async def test_get_payment_status_success(self, client):
        """Тест получения статуса платежа"""
        with patch('app.api.routers.webhooks.get_payment_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_result = PaymentResult(
                payment_id="test_payment_123",
                status=PaymentStatus.SUCCEEDED,
                amount=Decimal("100.00"),
                currency="RUB",
                provider=PaymentProvider.YOOKASSA
            )
            mock_service.get_payment_status.return_value = mock_result
            mock_get_service.return_value = mock_service
            
            response = client.get("/webhooks/status/test_payment_123?provider=yookassa")
            
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["status"] == "ok"
            assert "payment" in response_data
            assert response_data["payment"]["payment_id"] == "test_payment_123"
    
    async def test_get_payment_status_unsupported_provider(self, client):
        """Тест получения статуса платежа с неподдерживаемым провайдером"""
        response = client.get("/webhooks/status/test_payment_123?provider=invalid")
        
        assert response.status_code == 400
        assert "Unsupported provider" in response.json()["detail"]
    
    async def test_background_task_processing(self, client):
        """Тест фоновой обработки успешного платежа"""
        webhook_data = {
            "event": "payment.succeeded",
            "object": {
                "id": "test_payment_123",
                "status": "succeeded",
                "amount": {"value": "100.00", "currency": "RUB"},
                "metadata": {
                    "plan_id": "1",
                    "telegram_user_id": "123456789"
                }
            }
        }
        
        with patch('app.api.routers.webhooks.get_payment_service') as mock_get_service, \
             patch('app.api.routers.webhooks.process_successful_payment_background') as mock_bg_task:
            
            mock_service = AsyncMock()
            mock_result = PaymentResult(
                payment_id="test_payment_123",
                status=PaymentStatus.SUCCEEDED,
                amount=Decimal("100.00"),
                currency="RUB",
                provider=PaymentProvider.YOOKASSA,
                metadata={
                    "plan_id": "1",
                    "telegram_user_id": "123456789"
                }
            )
            mock_service.process_webhook.return_value = mock_result
            mock_get_service.return_value = mock_service
            
            response = client.post(
                "/webhooks/yookassa",
                json=webhook_data,
                headers={"Content-Type": "application/json"}
            )
            
            assert response.status_code == 200
            # Фоновая задача должна быть добавлена, но мы не можем проверить её выполнение в синхронном тесте


@pytest.mark.asyncio
class TestBackgroundPaymentProcessing:
    """Тесты для фоновой обработки платежей"""
    
    async def test_process_successful_payment_background_success(self):
        """Тест успешной фоновой обработки платежа"""
        from app.api.routers.webhooks import process_successful_payment_background
        
        mock_payment_result = PaymentResult(
            payment_id="test_payment_123",
            status=PaymentStatus.SUCCEEDED,
            amount=Decimal("100.00"),
            currency="RUB",
            provider=PaymentProvider.YOOKASSA,
            metadata={
                "plan_id": "1",
                "telegram_user_id": "123456789"
            }
        )
        
        with patch('app.services.subscription_service.SubscriptionService') as mock_sub_service, \
             patch('app.services.notification_service.NotificationService') as mock_notif_service:
            
            # Мокаем сервисы
            mock_subscription_instance = AsyncMock()
            mock_subscription = AsyncMock()
            mock_subscription.id = 1
            mock_subscription_instance.create_subscription.return_value = mock_subscription
            mock_sub_service.return_value = mock_subscription_instance
            
            mock_notification_instance = AsyncMock()
            mock_notif_service.return_value = mock_notification_instance
            
            # Мокаем сессию БД
            mock_session = AsyncMock()
            
            # Выполняем фоновую обработку
            await process_successful_payment_background(mock_payment_result, mock_session)
            
            # Проверяем, что подписка была создана
            mock_subscription_instance.create_subscription.assert_called_once_with(
                user_id=123456789,
                plan_id=1
            )
            
            # Проверяем, что уведомление было отправлено
            mock_notification_instance.send_subscription_activated.assert_called_once_with(
                user_id=123456789,
                subscription=mock_subscription
            )
    
    async def test_process_successful_payment_background_missing_data(self):
        """Тест фоновой обработки с недостающими данными"""
        from app.api.routers.webhooks import process_successful_payment_background
        
        mock_payment_result = PaymentResult(
            payment_id="test_payment_123",
            status=PaymentStatus.SUCCEEDED,
            amount=Decimal("100.00"),
            currency="RUB",
            provider=PaymentProvider.YOOKASSA,
            metadata={}  # Нет необходимых данных
        )
        
        mock_session = AsyncMock()
        
        # Выполняем фоновую обработку (не должно вызывать исключений)
        await process_successful_payment_background(mock_payment_result, mock_session)
        
        # Проверяем, что никакие сервисы не были вызваны
        with patch('app.services.subscription_service.SubscriptionService') as mock_sub_service:
            mock_sub_service.assert_not_called()
