"""
Тесты для сервиса платежей
"""

import pytest
from decimal import Decimal
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone

from app.services.payment_service import (
    PaymentService, PaymentProvider, PaymentStatus, PaymentResult,
    PaymentProviderInterface
)
from app.database.models import Transaction, TransactionStatus


class MockPaymentProvider(PaymentProviderInterface):
    """Мок провайдер для тестирования"""
    
    def __init__(self, provider_type: PaymentProvider):
        self.provider_type = provider_type
        self.payments = {}
    
    async def create_payment(self, amount, currency, description, return_url=None, metadata=None):
        payment_id = f"test_payment_{len(self.payments) + 1}"
        result = PaymentResult(
            payment_id=payment_id,
            status=PaymentStatus.PENDING,
            payment_url=f"https://test.com/pay/{payment_id}",
            amount=amount,
            currency=currency,
            provider=self.provider_type,
            metadata=metadata or {}
        )
        self.payments[payment_id] = result
        return result
    
    async def get_payment_status(self, payment_id):
        if payment_id in self.payments:
            return self.payments[payment_id]
        raise Exception("Payment not found")
    
    async def cancel_payment(self, payment_id):
        if payment_id in self.payments:
            self.payments[payment_id].status = PaymentStatus.CANCELED
            return self.payments[payment_id]
        raise Exception("Payment not found")
    
    async def refund_payment(self, payment_id, amount=None, reason=None):
        if payment_id in self.payments:
            original = self.payments[payment_id]
            refund_result = PaymentResult(
                payment_id=f"refund_{payment_id}",
                status=PaymentStatus.SUCCEEDED,
                amount=amount or original.amount,
                currency=original.currency,
                provider=self.provider_type,
                metadata={"original_payment_id": payment_id}
            )
            return refund_result
        raise Exception("Payment not found")
    
    def validate_webhook(self, headers, body):
        return True
    
    async def process_webhook(self, headers, body):
        payment_id = body.get("payment_id")
        if payment_id and payment_id in self.payments:
            self.payments[payment_id].status = PaymentStatus.SUCCEEDED
            return self.payments[payment_id]
        return None


@pytest.fixture
def payment_service(db_session):
    """Фикстура для создания сервиса платежей"""
    service = PaymentService(db_session)

    # Регистрируем мок провайдеров
    yookassa_provider = MockPaymentProvider(PaymentProvider.YOOKASSA)
    cryptomus_provider = MockPaymentProvider(PaymentProvider.CRYPTOMUS)

    service.register_provider(PaymentProvider.YOOKASSA, yookassa_provider)
    service.register_provider(PaymentProvider.CRYPTOMUS, cryptomus_provider)

    return service


@pytest.mark.asyncio
class TestPaymentService:
    """Тесты для PaymentService"""
    
    async def test_register_provider(self, payment_service):
        """Тест регистрации провайдера"""
        providers = payment_service.get_available_providers()
        assert PaymentProvider.YOOKASSA in providers
        assert PaymentProvider.CRYPTOMUS in providers
        assert len(providers) == 2
    
    async def test_create_payment_success(self, payment_service):
        """Тест успешного создания платежа"""
        with patch('app.database.crud.create_transaction') as mock_create:
            mock_create.return_value = AsyncMock()
            
            result = await payment_service.create_payment(
                provider=PaymentProvider.YOOKASSA,
                amount=Decimal("100.00"),
                currency="RUB",
                description="Test payment",
                user_id=123,
                subscription_plan_id=1
            )
            
            assert result.payment_id.startswith("test_payment_")
            assert result.status == PaymentStatus.PENDING
            assert result.amount == Decimal("100.00")
            assert result.currency == "RUB"
            assert result.provider == PaymentProvider.YOOKASSA
            assert result.payment_url is not None
            
            # Проверяем, что транзакция была сохранена
            mock_create.assert_called_once()
    
    async def test_create_payment_unsupported_provider(self, payment_service):
        """Тест создания платежа с неподдерживаемым провайдером"""
        with pytest.raises(ValueError, match="не поддерживается"):
            await payment_service.create_payment(
                provider=PaymentProvider.TELEGRAM_STARS,  # Не зарегистрирован
                amount=Decimal("100.00"),
                currency="XTR",
                description="Test payment",
                user_id=123
            )
    
    async def test_get_payment_status(self, payment_service):
        """Тест получения статуса платежа"""
        # Сначала создаем платеж
        with patch('app.database.crud.create_transaction') as mock_create:
            mock_create.return_value = AsyncMock()
            
            result = await payment_service.create_payment(
                provider=PaymentProvider.YOOKASSA,
                amount=Decimal("100.00"),
                currency="RUB",
                description="Test payment",
                user_id=123
            )
            
            # Получаем статус
            status = await payment_service.get_payment_status(
                payment_id=result.payment_id,
                provider=PaymentProvider.YOOKASSA
            )
            
            assert status.payment_id == result.payment_id
            assert status.status == PaymentStatus.PENDING
    
    async def test_process_webhook_success(self, payment_service):
        """Тест успешной обработки webhook"""
        # Создаем платеж
        with patch('app.database.crud.create_transaction') as mock_create:
            mock_create.return_value = AsyncMock()
            
            payment_result = await payment_service.create_payment(
                provider=PaymentProvider.YOOKASSA,
                amount=Decimal("100.00"),
                currency="RUB",
                description="Test payment",
                user_id=123
            )
        
        # Обрабатываем webhook
        with patch('app.database.crud.update_transaction_by_payment_id') as mock_update:
            mock_update.return_value = AsyncMock()
            
            webhook_body = {"payment_id": payment_result.payment_id}
            result = await payment_service.process_webhook(
                provider=PaymentProvider.YOOKASSA,
                headers={},
                body=webhook_body
            )
            
            assert result is not None
            assert result.payment_id == payment_result.payment_id
            assert result.status == PaymentStatus.SUCCEEDED
            
            # Проверяем, что статус был обновлен
            mock_update.assert_called_once()
    
    async def test_process_webhook_invalid_provider(self, payment_service):
        """Тест обработки webhook от неподдерживаемого провайдера"""
        result = await payment_service.process_webhook(
            provider=PaymentProvider.TELEGRAM_STARS,  # Не зарегистрирован
            headers={},
            body={"payment_id": "test"}
        )
        
        assert result is None
    
    async def test_payment_status_mapping(self, payment_service):
        """Тест маппинга статусов платежа в статусы транзакции"""
        mapping_tests = [
            (PaymentStatus.PENDING, TransactionStatus.PENDING),
            (PaymentStatus.WAITING_FOR_CAPTURE, TransactionStatus.PENDING),
            (PaymentStatus.SUCCEEDED, TransactionStatus.COMPLETED),
            (PaymentStatus.CANCELED, TransactionStatus.CANCELLED),
            (PaymentStatus.FAILED, TransactionStatus.FAILED)
        ]
        
        for payment_status, expected_transaction_status in mapping_tests:
            result = payment_service._map_payment_status_to_transaction_status(payment_status)
            assert result == expected_transaction_status
    
    async def test_save_transaction(self, payment_service):
        """Тест сохранения транзакции"""
        with patch('app.database.crud.create_transaction') as mock_create:
            mock_transaction = MagicMock()
            mock_create.return_value = mock_transaction
            
            await payment_service._save_transaction(
                payment_id="test_payment_123",
                provider=PaymentProvider.YOOKASSA,
                amount=Decimal("100.00"),
                currency="RUB",
                user_id=123,
                subscription_plan_id=1,
                status=TransactionStatus.PENDING,
                metadata={"test": True}
            )
            
            # Проверяем, что create_transaction был вызван с правильными параметрами
            mock_create.assert_called_once()
            call_args = mock_create.call_args[0][1]  # Второй аргумент (transaction_data)
            
            assert call_args["payment_id"] == "test_payment_123"
            assert call_args["payment_system"] == "yookassa"
            assert call_args["amount"] == Decimal("100.00")
            assert call_args["currency"] == "RUB"
            assert call_args["user_id"] == 123
            assert call_args["subscription_id"] == 1
            assert call_args["status"] == TransactionStatus.PENDING
            assert call_args["metadata"] == {"test": True}
    
    async def test_update_transaction_status(self, payment_service):
        """Тест обновления статуса транзакции"""
        with patch('app.database.crud.update_transaction_by_payment_id') as mock_update:
            mock_transaction = MagicMock()
            mock_update.return_value = mock_transaction
            
            await payment_service._update_transaction_status(
                payment_id="test_payment_123",
                status=TransactionStatus.COMPLETED,
                metadata={"updated": True}
            )
            
            # Проверяем, что update_transaction_by_payment_id был вызван
            mock_update.assert_called_once_with(
                payment_service.db_session,
                "test_payment_123",
                {
                    "status": TransactionStatus.COMPLETED,
                    "metadata": {"updated": True}
                }
            )


class TestPaymentResult:
    """Тесты для PaymentResult"""

    def test_payment_result_creation(self):
        """Тест создания PaymentResult"""
        result = PaymentResult(
            payment_id="test_123",
            status=PaymentStatus.SUCCEEDED,
            payment_url="https://test.com/pay",
            amount=Decimal("100.00"),
            currency="RUB",
            provider=PaymentProvider.YOOKASSA,
            metadata={"test": True}
        )

        assert result.payment_id == "test_123"
        assert result.status == PaymentStatus.SUCCEEDED
        assert result.payment_url == "https://test.com/pay"
        assert result.amount == Decimal("100.00")
        assert result.currency == "RUB"
        assert result.provider == PaymentProvider.YOOKASSA
        assert result.metadata == {"test": True}
        assert isinstance(result.created_at, datetime)

    def test_payment_result_to_dict(self):
        """Тест преобразования PaymentResult в словарь"""
        result = PaymentResult(
            payment_id="test_123",
            status=PaymentStatus.SUCCEEDED,
            amount=Decimal("100.00"),
            currency="RUB",
            provider=PaymentProvider.YOOKASSA
        )

        result_dict = result.to_dict()

        assert result_dict["payment_id"] == "test_123"
        assert result_dict["status"] == "succeeded"
        assert result_dict["amount"] == 100.0
        assert result_dict["currency"] == "RUB"
        assert result_dict["provider"] == "yookassa"
        assert "created_at" in result_dict
