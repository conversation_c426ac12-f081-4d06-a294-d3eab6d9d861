"""
Тесты для работы с базой данных
"""

import pytest
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import StaticPool
from app.database.database import Base
from app.database.models import User, SubscriptionPlan, UserSubscription
from app.database.crud import (
    get_or_create_user, get_user_by_telegram_id,
    get_active_subscription_plans, get_subscription_plan_by_id,
    create_user_subscription, get_user_subscriptions
)


# Тестовая база данных в памяти
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"

# Создаем тестовый движок
test_engine = create_async_engine(
    TEST_DATABASE_URL,
    poolclass=StaticPool,
    connect_args={"check_same_thread": False},
    echo=False
)

# Фабрика тестовых сессий
TestSessionLocal = async_sessionmaker(
    test_engine,
    class_=AsyncSession,
    expire_on_commit=False
)


class TestUserCRUD:
    """Тесты для операций с пользователями"""

    @pytest.mark.asyncio
    async def test_create_new_user(self):
        """Тест создания нового пользователя"""
        # Создаем таблицы
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        # Тестовые данные
        user_data = {
            'telegram_id': 987654321,
            'username': 'newuser',
            'first_name': 'New',
            'last_name': 'User',
            'language_code': 'en'
        }

        # Создаем сессию и тестируем
        async with TestSessionLocal() as session:
            user = await get_or_create_user(user_data, session)

            assert user is not None
            assert user.telegram_id == user_data['telegram_id']
            assert user.username == user_data['username']
            assert user.first_name == user_data['first_name']
            assert user.referral_code is not None
            assert len(user.referral_code) == 8

        # Удаляем таблицы
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)

    @pytest.mark.asyncio
    async def test_get_user_by_telegram_id(self):
        """Тест получения пользователя по Telegram ID"""
        # Создаем таблицы
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        # Создаем пользователя
        async with TestSessionLocal() as session:
            user_data = {
                'telegram_id': 123456789,
                'username': 'testuser',
                'first_name': 'Test',
                'last_name': 'User',
                'language_code': 'ru'
            }

            # Создаем пользователя
            created_user = await get_or_create_user(user_data, session)

            # Получаем пользователя
            found_user = await get_user_by_telegram_id(created_user.telegram_id, session)

            assert found_user is not None
            assert found_user.id == created_user.id
            assert found_user.telegram_id == created_user.telegram_id

        # Удаляем таблицы
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)

    @pytest.mark.asyncio
    async def test_get_nonexistent_user(self):
        """Тест получения несуществующего пользователя"""
        # Создаем таблицы
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        async with TestSessionLocal() as session:
            user = await get_user_by_telegram_id(999999999, session)
            assert user is None

        # Удаляем таблицы
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)


class TestSubscriptionPlanCRUD:
    """Тесты для операций с тарифными планами"""

    @pytest.mark.asyncio
    async def test_get_active_plans(self):
        """Тест получения активных планов"""
        # Создаем таблицы
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        async with TestSessionLocal() as session:
            # Создаем активный план
            active_plan = SubscriptionPlan(
                name="Активный план",
                description="Тестовый активный план",
                price=299.00,
                duration_days=30,
                is_active=True,
                sort_order=1
            )

            # Создаем неактивный план
            inactive_plan = SubscriptionPlan(
                name="Неактивный план",
                description="Тестовый неактивный план",
                price=199.00,
                duration_days=15,
                is_active=False,
                sort_order=2
            )

            session.add(active_plan)
            session.add(inactive_plan)
            await session.commit()

            plans = await get_active_subscription_plans(session)

            assert len(plans) == 1
            assert plans[0].name == "Активный план"
            assert plans[0].is_active is True

        # Удаляем таблицы
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)

    @pytest.mark.asyncio
    async def test_get_plan_by_id(self):
        """Тест получения плана по ID"""
        # Создаем таблицы
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        async with TestSessionLocal() as session:
            # Создаем план
            plan = SubscriptionPlan(
                name="Тестовый план",
                description="Описание тестового плана",
                price=399.00,
                duration_days=30,
                is_active=True,
                sort_order=1
            )

            session.add(plan)
            await session.commit()
            await session.refresh(plan)

            found_plan = await get_subscription_plan_by_id(plan.id, session)

            assert found_plan is not None
            assert found_plan.id == plan.id
            assert found_plan.name == "Тестовый план"

        # Удаляем таблицы
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)

    @pytest.mark.asyncio
    async def test_get_nonexistent_plan(self):
        """Тест получения несуществующего плана"""
        # Создаем таблицы
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        async with TestSessionLocal() as session:
            plan = await get_subscription_plan_by_id(999, session)
            assert plan is None

        # Удаляем таблицы
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)


class TestUserSubscriptionCRUD:
    """Тесты для операций с подписками"""

    @pytest.mark.asyncio
    async def test_create_subscription(self):
        """Тест создания подписки"""
        # Создаем таблицы
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        async with TestSessionLocal() as session:
            # Создаем пользователя
            user_data = {
                'telegram_id': 123456789,
                'username': 'testuser',
                'first_name': 'Test',
                'language_code': 'ru'
            }
            user = await get_or_create_user(user_data, session)

            # Создаем план
            plan = SubscriptionPlan(
                name="Тестовый план",
                description="Описание плана",
                price=299.00,
                duration_days=30,
                is_active=True,
                sort_order=1
            )
            session.add(plan)
            await session.commit()
            await session.refresh(plan)

            # Создаем подписку
            subscription = await create_user_subscription(
                user_id=user.id,
                plan_id=plan.id,
                marzban_user_id="test_marzban_user",
                db=session
            )

            assert subscription is not None
            assert subscription.user_id == user.id
            assert subscription.plan_id == plan.id
            assert subscription.marzban_user_id == "test_marzban_user"
            assert subscription.status == "active"
            assert subscription.start_date is not None
            assert subscription.end_date is not None

        # Удаляем таблицы
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)

    @pytest.mark.asyncio
    async def test_get_user_subscriptions(self):
        """Тест получения подписок пользователя"""
        # Создаем таблицы
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        async with TestSessionLocal() as session:
            # Создаем пользователя
            user_data = {
                'telegram_id': 987654321,
                'username': 'testuser2',
                'first_name': 'Test2',
                'language_code': 'ru'
            }
            user = await get_or_create_user(user_data, session)

            # Создаем план
            plan = SubscriptionPlan(
                name="Тестовый план 2",
                description="Описание плана 2",
                price=199.00,
                duration_days=15,
                is_active=True,
                sort_order=1
            )
            session.add(plan)
            await session.commit()
            await session.refresh(plan)

            # Создаем подписку
            await create_user_subscription(
                user_id=user.id,
                plan_id=plan.id,
                db=session
            )

            subscriptions = await get_user_subscriptions(user.id, session)

            assert len(subscriptions) == 1
            assert subscriptions[0].user_id == user.id

        # Удаляем таблицы
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)

    @pytest.mark.asyncio
    async def test_get_empty_subscriptions(self):
        """Тест получения пустого списка подписок"""
        # Создаем таблицы
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        async with TestSessionLocal() as session:
            subscriptions = await get_user_subscriptions(999, session)
            assert len(subscriptions) == 0

        # Удаляем таблицы
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
