"""
Тесты для работы с базой данных
"""

import pytest
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import StaticPool
from app.database.database import Base
from app.database.models import User, SubscriptionPlan, UserSubscription
from app.database.crud import (
    get_or_create_user, get_user_by_telegram_id,
    get_active_subscription_plans, get_subscription_plan_by_id,
    create_user_subscription, get_user_subscriptions
)


# Тестовая база данных в памяти
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"

# Создаем тестовый движок
test_engine = create_async_engine(
    TEST_DATABASE_URL,
    poolclass=StaticPool,
    connect_args={"check_same_thread": False},
    echo=False
)

# Фабрика тестовых сессий
TestSessionLocal = async_sessionmaker(
    test_engine,
    class_=AsyncSession,
    expire_on_commit=False
)


class TestUserCRUD:
    """Тесты для операций с пользователями"""

    @pytest.mark.asyncio
    async def test_create_new_user(self):
        """Тест создания нового пользователя"""
        # Создаем таблицы
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        # Тестовые данные
        user_data = {
            'telegram_id': 987654321,
            'username': 'newuser',
            'first_name': 'New',
            'last_name': 'User',
            'language_code': 'en'
        }

        # Создаем сессию и тестируем
        async with TestSessionLocal() as session:
            user = await get_or_create_user(user_data, session)

            assert user is not None
            assert user.telegram_id == user_data['telegram_id']
            assert user.username == user_data['username']
            assert user.first_name == user_data['first_name']
            assert user.referral_code is not None
            assert len(user.referral_code) == 8

        # Удаляем таблицы
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)

    @pytest.mark.asyncio
    async def test_get_user_by_telegram_id(self):
        """Тест получения пользователя по Telegram ID"""
        # Создаем таблицы
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        # Создаем пользователя
        async with TestSessionLocal() as session:
            user_data = {
                'telegram_id': 123456789,
                'username': 'testuser',
                'first_name': 'Test',
                'last_name': 'User',
                'language_code': 'ru'
            }

            # Создаем пользователя
            created_user = await get_or_create_user(user_data, session)

            # Получаем пользователя
            found_user = await get_user_by_telegram_id(created_user.telegram_id, session)

            assert found_user is not None
            assert found_user.id == created_user.id
            assert found_user.telegram_id == created_user.telegram_id

        # Удаляем таблицы
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)

    @pytest.mark.asyncio
    async def test_get_nonexistent_user(self):
        """Тест получения несуществующего пользователя"""
        # Создаем таблицы
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        async with TestSessionLocal() as session:
            user = await get_user_by_telegram_id(999999999, session)
            assert user is None

        # Удаляем таблицы
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)


class TestSubscriptionPlanCRUD:
    """Тесты для операций с тарифными планами"""
    
    @pytest.mark.asyncio
    async def test_get_active_plans(self, db_session: AsyncSession, sample_plan):
        """Тест получения активных планов"""
        # Создаем неактивный план
        inactive_plan = SubscriptionPlan(
            name="Неактивный план",
            price=199.00,
            duration_days=15,
            is_active=False
        )
        db_session.add(inactive_plan)
        await db_session.commit()
        
        plans = await get_active_subscription_plans()
        
        assert len(plans) == 1
        assert plans[0].id == sample_plan.id
        assert plans[0].is_active is True
    
    @pytest.mark.asyncio
    async def test_get_plan_by_id(self, db_session: AsyncSession, sample_plan):
        """Тест получения плана по ID"""
        plan = await get_subscription_plan_by_id(sample_plan.id)
        
        assert plan is not None
        assert plan.id == sample_plan.id
        assert plan.name == sample_plan.name
    
    @pytest.mark.asyncio
    async def test_get_nonexistent_plan(self, db_session: AsyncSession):
        """Тест получения несуществующего плана"""
        plan = await get_subscription_plan_by_id(999)
        
        assert plan is None


class TestUserSubscriptionCRUD:
    """Тесты для операций с подписками"""
    
    @pytest.mark.asyncio
    async def test_create_subscription(self, db_session: AsyncSession, sample_user, sample_plan):
        """Тест создания подписки"""
        subscription = await create_user_subscription(
            user_id=sample_user.telegram_id,
            plan_id=sample_plan.id,
            marzban_user_id="test_marzban_user"
        )
        
        assert subscription is not None
        assert subscription.user_id == sample_user.telegram_id
        assert subscription.plan_id == sample_plan.id
        assert subscription.marzban_user_id == "test_marzban_user"
        assert subscription.status == "active"
        assert subscription.start_date is not None
        assert subscription.end_date is not None
    
    @pytest.mark.asyncio
    async def test_get_user_subscriptions(self, db_session: AsyncSession, sample_user, sample_plan):
        """Тест получения подписок пользователя"""
        # Создаем подписку
        await create_user_subscription(
            user_id=sample_user.telegram_id,
            plan_id=sample_plan.id
        )
        
        subscriptions = await get_user_subscriptions(sample_user.telegram_id)
        
        assert len(subscriptions) == 1
        assert subscriptions[0].user_id == sample_user.telegram_id
        assert subscriptions[0].plan is not None
        assert subscriptions[0].plan.id == sample_plan.id
    
    @pytest.mark.asyncio
    async def test_get_empty_subscriptions(self, db_session: AsyncSession, sample_user):
        """Тест получения пустого списка подписок"""
        subscriptions = await get_user_subscriptions(sample_user.telegram_id)
        
        assert len(subscriptions) == 0
