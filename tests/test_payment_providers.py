"""
Тесты для провайдеров платежей
"""

import pytest
import json
from decimal import Decimal
from unittest.mock import AsyncMock, MagicMock, patch
from aiohttp import ClientSession, ClientResponse

from app.services.providers.yookassa_provider import YooKassaProvider
from app.services.providers.cryptomus_provider import CryptomusProvider
from app.services.providers.telegram_stars_provider import TelegramStarsProvider
from app.services.payment_service import PaymentStatus, PaymentProvider


@pytest.mark.asyncio
class TestYooKassaProvider:
    """Тесты для YooKassa провайдера"""
    
    @pytest.fixture
    def yookassa_provider(self):
        """Фикстура для создания YooKassa провайдера"""
        return YooKassaProvider(
            shop_id="test_shop_id",
            secret_key="test_secret_key"
        )
    
    async def test_create_payment_success(self, yookassa_provider):
        """Тест успешного создания платежа"""
        mock_response_data = {
            "id": "test_payment_123",
            "status": "pending",
            "amount": {"value": "100.00", "currency": "RUB"},
            "confirmation": {"confirmation_url": "https://yookassa.ru/pay/test"},
            "metadata": {"test": True}
        }
        
        with patch.object(yookassa_provider, '_get_session') as mock_get_session:
            mock_session = AsyncMock()
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json.return_value = mock_response_data
            
            mock_session.post.return_value.__aenter__.return_value = mock_response
            mock_get_session.return_value = mock_session
            
            result = await yookassa_provider.create_payment(
                amount=Decimal("100.00"),
                currency="RUB",
                description="Test payment"
            )
            
            assert result.payment_id == "test_payment_123"
            assert result.status == PaymentStatus.PENDING
            assert result.amount == Decimal("100.00")
            assert result.currency == "RUB"
            assert result.provider == PaymentProvider.YOOKASSA
            assert "yookassa.ru" in result.payment_url
    
    async def test_create_payment_error(self, yookassa_provider):
        """Тест ошибки создания платежа"""
        mock_response_data = {
            "description": "Invalid request"
        }
        
        with patch.object(yookassa_provider, '_get_session') as mock_get_session:
            mock_session = AsyncMock()
            mock_response = AsyncMock()
            mock_response.status = 400
            mock_response.json.return_value = mock_response_data
            
            mock_session.post.return_value.__aenter__.return_value = mock_response
            mock_get_session.return_value = mock_session
            
            with pytest.raises(Exception, match="Ошибка ЮKassa"):
                await yookassa_provider.create_payment(
                    amount=Decimal("100.00"),
                    currency="RUB",
                    description="Test payment"
                )
    
    async def test_get_payment_status(self, yookassa_provider):
        """Тест получения статуса платежа"""
        mock_response_data = {
            "id": "test_payment_123",
            "status": "succeeded",
            "amount": {"value": "100.00", "currency": "RUB"},
            "metadata": {"test": True}
        }
        
        with patch.object(yookassa_provider, '_get_session') as mock_get_session:
            mock_session = AsyncMock()
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json.return_value = mock_response_data
            
            mock_session.get.return_value.__aenter__.return_value = mock_response
            mock_get_session.return_value = mock_session
            
            result = await yookassa_provider.get_payment_status("test_payment_123")
            
            assert result.payment_id == "test_payment_123"
            assert result.status == PaymentStatus.SUCCEEDED
            assert result.amount == Decimal("100.00")
            assert result.currency == "RUB"
    
    async def test_process_webhook(self, yookassa_provider):
        """Тест обработки webhook"""
        webhook_body = {
            "event": "payment.succeeded",
            "object": {
                "id": "test_payment_123",
                "status": "succeeded",
                "amount": {"value": "100.00", "currency": "RUB"},
                "metadata": {"test": True}
            }
        }
        
        result = await yookassa_provider.process_webhook({}, webhook_body)
        
        assert result is not None
        assert result.payment_id == "test_payment_123"
        assert result.status == PaymentStatus.SUCCEEDED
        assert result.amount == Decimal("100.00")
    
    def test_validate_webhook(self, yookassa_provider):
        """Тест валидации webhook"""
        # YooKassa не использует подпись webhook'ов
        result = yookassa_provider.validate_webhook({}, b'{"test": true}')
        assert result is True
    
    def test_map_yookassa_status(self, yookassa_provider):
        """Тест маппинга статусов YooKassa"""
        status_mapping = {
            "pending": PaymentStatus.PENDING,
            "waiting_for_capture": PaymentStatus.WAITING_FOR_CAPTURE,
            "succeeded": PaymentStatus.SUCCEEDED,
            "canceled": PaymentStatus.CANCELED,
            "failed": PaymentStatus.FAILED
        }
        
        for yookassa_status, expected_status in status_mapping.items():
            result = yookassa_provider._map_yookassa_status(yookassa_status)
            assert result == expected_status


@pytest.mark.asyncio
class TestCryptomusProvider:
    """Тесты для Cryptomus провайдера"""
    
    @pytest.fixture
    def cryptomus_provider(self):
        """Фикстура для создания Cryptomus провайдера"""
        return CryptomusProvider(
            merchant_id="test_merchant_id",
            api_key="test_api_key"
        )
    
    async def test_create_payment_success(self, cryptomus_provider):
        """Тест успешного создания платежа"""
        mock_response_data = {
            "state": 0,
            "result": {
                "uuid": "test_payment_123",
                "url": "https://cryptomus.com/pay/test"
            }
        }
        
        with patch.object(cryptomus_provider, '_get_session') as mock_get_session:
            mock_session = AsyncMock()
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json.return_value = mock_response_data
            
            mock_session.post.return_value.__aenter__.return_value = mock_response
            mock_get_session.return_value = mock_session
            
            result = await cryptomus_provider.create_payment(
                amount=Decimal("10.00"),
                currency="USDT",
                description="Test payment"
            )
            
            assert result.payment_id == "test_payment_123"
            assert result.status == PaymentStatus.PENDING
            assert result.amount == Decimal("10.00")
            assert result.currency == "USDT"
            assert result.provider == PaymentProvider.CRYPTOMUS
            assert "cryptomus.com" in result.payment_url
    
    async def test_generate_signature(self, cryptomus_provider):
        """Тест генерации подписи"""
        test_data = {"amount": "10.00", "currency": "USDT"}
        signature = cryptomus_provider._generate_signature(test_data)
        
        assert isinstance(signature, str)
        assert len(signature) == 64  # SHA256 hex digest length
    
    def test_validate_webhook_valid(self, cryptomus_provider):
        """Тест валидации валидного webhook"""
        test_data = {"uuid": "test_123", "status": "paid"}
        test_signature = cryptomus_provider._generate_signature(test_data)
        
        headers = {"sign": test_signature}
        body = json.dumps(test_data).encode('utf-8')
        
        result = cryptomus_provider.validate_webhook(headers, body)
        assert result is True
    
    def test_validate_webhook_invalid(self, cryptomus_provider):
        """Тест валидации невалидного webhook"""
        headers = {"sign": "invalid_signature"}
        body = b'{"uuid": "test_123", "status": "paid"}'
        
        result = cryptomus_provider.validate_webhook(headers, body)
        assert result is False
    
    async def test_process_webhook(self, cryptomus_provider):
        """Тест обработки webhook"""
        webhook_body = {
            "uuid": "test_payment_123",
            "status": "paid",
            "amount": "10.00",
            "currency": "USDT"
        }
        
        result = await cryptomus_provider.process_webhook({}, webhook_body)
        
        assert result is not None
        assert result.payment_id == "test_payment_123"
        assert result.status == PaymentStatus.SUCCEEDED
        assert result.amount == Decimal("10.00")
        assert result.currency == "USDT"
    
    def test_map_cryptomus_status(self, cryptomus_provider):
        """Тест маппинга статусов Cryptomus"""
        status_mapping = {
            "check": PaymentStatus.PENDING,
            "process": PaymentStatus.PENDING,
            "paid": PaymentStatus.SUCCEEDED,
            "paid_over": PaymentStatus.SUCCEEDED,
            "fail": PaymentStatus.FAILED,
            "cancel": PaymentStatus.CANCELED,
            "system_fail": PaymentStatus.FAILED
        }
        
        for cryptomus_status, expected_status in status_mapping.items():
            result = cryptomus_provider._map_cryptomus_status(cryptomus_status)
            assert result == expected_status


@pytest.mark.asyncio
class TestTelegramStarsProvider:
    """Тесты для Telegram Stars провайдера"""
    
    @pytest.fixture
    def telegram_stars_provider(self):
        """Фикстура для создания Telegram Stars провайдера"""
        mock_bot = AsyncMock()
        return TelegramStarsProvider(mock_bot)
    
    async def test_create_payment_success(self, telegram_stars_provider):
        """Тест успешного создания платежа"""
        result = await telegram_stars_provider.create_payment(
            amount=Decimal("50"),  # 50 звезд
            currency="XTR",
            description="Test payment"
        )
        
        assert result.payment_id is not None
        assert result.status == PaymentStatus.PENDING
        assert result.amount == Decimal("50")
        assert result.currency == "XTR"
        assert result.provider == PaymentProvider.TELEGRAM_STARS
        assert result.payment_url is None  # Для Telegram Stars URL не нужен
        
        # Проверяем метаданные
        assert "prices" in result.metadata
        assert "payload" in result.metadata
        assert result.metadata["prices"][0]["amount"] == 5000  # 50 звезд * 100 копеек
    
    async def test_create_payment_invalid_currency(self, telegram_stars_provider):
        """Тест создания платежа с неверной валютой"""
        with pytest.raises(ValueError, match="поддерживает только валюту XTR"):
            await telegram_stars_provider.create_payment(
                amount=Decimal("100"),
                currency="RUB",  # Неверная валюта
                description="Test payment"
            )
    
    async def test_send_invoice_success(self, telegram_stars_provider):
        """Тест успешной отправки инвойса"""
        # Создаем платеж
        payment_result = await telegram_stars_provider.create_payment(
            amount=Decimal("50"),
            currency="XTR",
            description="Test payment"
        )
        
        # Отправляем инвойс
        result = await telegram_stars_provider.send_invoice(
            chat_id=123456789,
            payment_result=payment_result
        )
        
        assert result is True
        
        # Проверяем, что send_invoice был вызван
        telegram_stars_provider.bot.send_invoice.assert_called_once()
    
    async def test_process_successful_payment(self, telegram_stars_provider):
        """Тест обработки успешного платежа"""
        payment_data = {
            "invoice_payload": '{"payment_id": "test_123", "metadata": {"test": true}}',
            "total_amount": 5000,  # 50 звезд в копейках
            "currency": "XTR",
            "telegram_payment_charge_id": "tg_charge_123",
            "provider_payment_charge_id": "provider_charge_123"
        }
        
        result = await telegram_stars_provider.process_successful_payment(payment_data)
        
        assert result.payment_id == "test_123"
        assert result.status == PaymentStatus.SUCCEEDED
        assert result.amount == Decimal("50")  # Конвертировано из копеек
        assert result.currency == "XTR"
        assert result.provider == PaymentProvider.TELEGRAM_STARS
        
        # Проверяем метаданные
        assert result.metadata["telegram_payment_charge_id"] == "tg_charge_123"
        assert result.metadata["provider_payment_charge_id"] == "provider_charge_123"
