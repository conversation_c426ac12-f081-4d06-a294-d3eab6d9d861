"""
Тесты для утилит
"""

import pytest
from app.core.utils import (
    generate_referral_code, generate_random_string,
    validate_telegram_id, sanitize_username,
    create_marzban_username, format_bytes
)


class TestUtilityFunctions:
    """Тесты для вспомогательных функций"""
    
    def test_generate_referral_code(self):
        """Тест генерации реферального кода"""
        code = generate_referral_code()
        
        assert len(code) == 8
        assert code.isupper()
        assert code.isalnum()
    
    def test_generate_referral_code_custom_length(self):
        """Тест генерации реферального кода с кастомной длиной"""
        code = generate_referral_code(12)
        
        assert len(code) == 12
        assert code.isupper()
        assert code.isalnum()
    
    def test_generate_random_string(self):
        """Тест генерации случайной строки"""
        string = generate_random_string()
        
        assert len(string) == 32
        assert string.isalnum()
    
    def test_generate_random_string_custom_length(self):
        """Тест генерации случайной строки с кастомной длиной"""
        string = generate_random_string(16)
        
        assert len(string) == 16
        assert string.isalnum()
    
    def test_validate_telegram_id_valid(self):
        """Тест валидации корректного Telegram ID"""
        assert validate_telegram_id(123456789) is True
        assert validate_telegram_id("123456789") is True
        assert validate_telegram_id(1) is True
    
    def test_validate_telegram_id_invalid(self):
        """Тест валидации некорректного Telegram ID"""
        assert validate_telegram_id(0) is False
        assert validate_telegram_id(-1) is False
        assert validate_telegram_id("invalid") is False
        assert validate_telegram_id(None) is False
        assert validate_telegram_id(10**13) is False  # Слишком большой ID
    
    def test_sanitize_username(self):
        """Тест очистки имени пользователя"""
        assert sanitize_username("TestUser123") == "testuser123"
        assert sanitize_username("<EMAIL>") == "userdomain.com"
        assert sanitize_username("user_name-123") == "user_name-123"

        # Для кириллицы должно генерироваться случайное имя
        result = sanitize_username("Пользователь")
        assert result.startswith("user_")
        assert len(result) == 13  # "user_" + 8 символов
    
    def test_sanitize_username_empty(self):
        """Тест очистки пустого имени пользователя"""
        result = sanitize_username("")
        
        assert result.startswith("user_")
        assert len(result) == 13  # "user_" + 8 символов
    
    def test_create_marzban_username(self):
        """Тест создания имени пользователя для Marzban"""
        username = create_marzban_username(123456789)
        
        assert username == "unveil_123456789"
    
    def test_create_marzban_username_with_suffix(self):
        """Тест создания имени пользователя для Marzban с суффиксом"""
        username = create_marzban_username(123456789, "premium")
        
        assert username == "unveil_123456789_premium"
    
    def test_create_marzban_username_length_limit(self):
        """Тест ограничения длины имени пользователя для Marzban"""
        long_suffix = "a" * 50
        username = create_marzban_username(123456789, long_suffix)
        
        assert len(username) <= 50
    
    def test_format_bytes(self):
        """Тест форматирования байтов"""
        assert format_bytes(0) == "0.0 B"
        assert format_bytes(1024) == "1.0 KB"
        assert format_bytes(1024 * 1024) == "1.0 MB"
        assert format_bytes(1024 * 1024 * 1024) == "1.0 GB"
        assert format_bytes(1536) == "1.5 KB"  # 1.5 KB
    
    def test_format_bytes_large_values(self):
        """Тест форматирования больших значений"""
        tb_value = 1024 * 1024 * 1024 * 1024
        assert format_bytes(tb_value) == "1.0 TB"
        
        pb_value = tb_value * 1024
        assert format_bytes(pb_value) == "1.0 PB"
