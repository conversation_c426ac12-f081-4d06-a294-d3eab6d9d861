"""
Тесты сервиса управления подписками
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone, timedelta

from app.services.subscription_service import SubscriptionService
from app.database.models import (
    User, SubscriptionPlan, UserSubscription, SubscriptionStatus
)
from app.integrations.marzban import (
    MarzbanClient, MarzbanUserCreate, MarzbanUserUpdate,
    MarzbanUserStatus, MarzbanUserNotFoundError,
    MarzbanUserAlreadyExistsError
)


class TestSubscriptionService:
    """Тесты сервиса управления подписками"""
    
    @pytest.fixture
    def mock_db_session(self):
        """Мок сессии базы данных"""
        session = AsyncMock()
        session.commit = AsyncMock()
        session.rollback = AsyncMock()
        session.execute = AsyncMock()
        return session
    
    @pytest.fixture
    def mock_marzban_client(self):
        """Мок клиента Marzban"""
        client = AsyncMock(spec=MarzbanClient)
        client.__aenter__ = AsyncMock(return_value=client)
        client.__aexit__ = AsyncMock(return_value=None)
        return client
    
    @pytest.fixture
    def subscription_service(self, mock_db_session, mock_marzban_client):
        """Сервис подписок для тестов"""
        return SubscriptionService(mock_db_session, mock_marzban_client)
    
    @pytest.fixture
    def sample_user(self):
        """Образец пользователя"""
        user = User()
        user.id = 1
        user.telegram_id = 123456789
        user.username = "testuser"
        user.first_name = "Test"
        user.last_name = "User"
        user.language_code = "ru"
        user.is_premium = False
        user.created_at = datetime.now(timezone.utc)
        return user
    
    @pytest.fixture
    def sample_plan(self):
        """Образец тарифного плана"""
        plan = SubscriptionPlan()
        plan.id = 1
        plan.name = "Базовый"
        plan.description = "Базовый тарифный план"
        plan.price = 299.0
        plan.currency = "RUB"
        plan.duration_days = 30
        plan.traffic_limit = 107374182400  # 100 GB
        plan.max_devices = 3
        plan.is_active = True
        plan.marzban_config = {
            "vmess": {"id": "test-uuid"},
            "vless": {"id": "test-uuid"}
        }
        return plan
    
    @pytest.fixture
    def sample_subscription(self, sample_user, sample_plan):
        """Образец подписки"""
        subscription = UserSubscription()
        subscription.id = 1
        subscription.user_id = sample_user.id
        subscription.plan_id = sample_plan.id
        subscription.status = SubscriptionStatus.ACTIVE
        subscription.start_date = datetime.now(timezone.utc)
        subscription.end_date = datetime.now(timezone.utc) + timedelta(days=30)
        subscription.traffic_limit = sample_plan.traffic_limit
        subscription.traffic_used = 0
        subscription.user = sample_user
        subscription.plan = sample_plan
        return subscription
    
    @pytest.mark.asyncio
    async def test_create_subscription_success(
        self, 
        subscription_service, 
        sample_user, 
        sample_plan,
        mock_db_session,
        mock_marzban_client
    ):
        """Тест успешного создания подписки"""
        # Настраиваем моки
        with patch('app.services.subscription_service.get_user_by_telegram_id') as mock_get_user:
            with patch('app.services.subscription_service.get_subscription_plan_by_id') as mock_get_plan:
                with patch('app.services.subscription_service.create_user_subscription') as mock_create_sub:
                    
                    mock_get_user.return_value = sample_user
                    mock_get_plan.return_value = sample_plan
                    
                    # Создаем мок подписки
                    mock_subscription = UserSubscription()
                    mock_subscription.id = 1
                    mock_subscription.user_id = sample_user.id
                    mock_subscription.plan_id = sample_plan.id
                    mock_subscription.status = SubscriptionStatus.PENDING
                    mock_subscription.user = sample_user
                    mock_subscription.plan = sample_plan
                    mock_create_sub.return_value = mock_subscription
                    
                    # Настраиваем мок для получения активных подписок
                    mock_scalars = MagicMock()
                    mock_scalars.all.return_value = []
                    mock_result = MagicMock()
                    mock_result.scalars.return_value = mock_scalars
                    mock_db_session.execute = AsyncMock(return_value=mock_result)
                    
                    # Настраиваем мок Marzban клиента
                    mock_marzban_client.create_user = AsyncMock()
                    
                    # Выполняем тест
                    result = await subscription_service.create_subscription(
                        user_telegram_id=123456789,
                        plan_id=1
                    )
                    
                    # Проверяем результат
                    assert result is not None
                    assert result.user_id == sample_user.id
                    assert result.plan_id == sample_plan.id
                    
                    # Проверяем вызовы
                    mock_get_user.assert_called_once_with(mock_db_session, 123456789)
                    mock_get_plan.assert_called_once_with(mock_db_session, 1)
                    mock_create_sub.assert_called_once()
                    mock_marzban_client.create_user.assert_called_once()
                    mock_db_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_subscription_user_not_found(
        self, 
        subscription_service,
        mock_db_session
    ):
        """Тест создания подписки для несуществующего пользователя"""
        with patch('app.services.subscription_service.get_user_by_telegram_id') as mock_get_user:
            mock_get_user.return_value = None
            
            with pytest.raises(ValueError, match="Пользователь .* не найден"):
                await subscription_service.create_subscription(
                    user_telegram_id=999999999,
                    plan_id=1
                )
    
    @pytest.mark.asyncio
    async def test_create_subscription_plan_not_found(
        self, 
        subscription_service,
        sample_user,
        mock_db_session
    ):
        """Тест создания подписки с несуществующим планом"""
        with patch('app.services.subscription_service.get_user_by_telegram_id') as mock_get_user:
            with patch('app.services.subscription_service.get_subscription_plan_by_id') as mock_get_plan:
                mock_get_user.return_value = sample_user
                mock_get_plan.return_value = None
                
                with pytest.raises(ValueError, match="План .* не найден"):
                    await subscription_service.create_subscription(
                        user_telegram_id=123456789,
                        plan_id=999
                    )
    
    @pytest.mark.asyncio
    async def test_create_subscription_marzban_user_exists(
        self, 
        subscription_service,
        sample_user,
        sample_plan,
        mock_db_session,
        mock_marzban_client
    ):
        """Тест создания подписки когда пользователь уже существует в Marzban"""
        with patch('app.services.subscription_service.get_user_by_telegram_id') as mock_get_user:
            with patch('app.services.subscription_service.get_subscription_plan_by_id') as mock_get_plan:
                with patch('app.services.subscription_service.create_user_subscription') as mock_create_sub:
                    
                    mock_get_user.return_value = sample_user
                    mock_get_plan.return_value = sample_plan
                    
                    mock_subscription = UserSubscription()
                    mock_subscription.id = 1
                    mock_subscription.user = sample_user
                    mock_subscription.plan = sample_plan
                    mock_create_sub.return_value = mock_subscription
                    
                    # Настраиваем мок для получения активных подписок
                    mock_scalars = MagicMock()
                    mock_scalars.all.return_value = []
                    mock_result = MagicMock()
                    mock_result.scalars.return_value = mock_scalars
                    mock_db_session.execute = AsyncMock(return_value=mock_result)
                    
                    # Настраиваем мок для случая, когда пользователь уже существует
                    mock_marzban_client.create_user = AsyncMock(
                        side_effect=MarzbanUserAlreadyExistsError("User already exists")
                    )
                    mock_marzban_client.update_user = AsyncMock()
                    
                    # Выполняем тест
                    result = await subscription_service.create_subscription(
                        user_telegram_id=123456789,
                        plan_id=1
                    )
                    
                    # Проверяем, что обновление было вызвано вместо создания
                    mock_marzban_client.update_user.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_activate_subscription_success(
        self,
        subscription_service,
        sample_subscription,
        mock_db_session,
        mock_marzban_client
    ):
        """Тест успешной активации подписки"""
        # Устанавливаем статус PENDING
        sample_subscription.status = SubscriptionStatus.PENDING
        
        with patch.object(subscription_service, '_get_subscription_by_id') as mock_get_sub:
            mock_get_sub.return_value = sample_subscription
            mock_marzban_client.update_user = AsyncMock()
            
            result = await subscription_service.activate_subscription(1)
            
            assert result.status == SubscriptionStatus.ACTIVE
            mock_marzban_client.update_user.assert_called_once()
            mock_db_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_activate_subscription_already_active(
        self,
        subscription_service,
        sample_subscription,
        mock_db_session
    ):
        """Тест активации уже активной подписки"""
        # Подписка уже активна
        sample_subscription.status = SubscriptionStatus.ACTIVE
        
        with patch.object(subscription_service, '_get_subscription_by_id') as mock_get_sub:
            mock_get_sub.return_value = sample_subscription
            
            result = await subscription_service.activate_subscription(1)
            
            assert result.status == SubscriptionStatus.ACTIVE
            # Commit не должен вызываться, так как изменений нет
            mock_db_session.commit.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_deactivate_subscription_success(
        self,
        subscription_service,
        sample_subscription,
        mock_db_session,
        mock_marzban_client
    ):
        """Тест успешной деактивации подписки"""
        with patch.object(subscription_service, '_get_subscription_by_id') as mock_get_sub:
            mock_get_sub.return_value = sample_subscription
            mock_marzban_client.update_user = AsyncMock()
            
            result = await subscription_service.deactivate_subscription(1, "Test reason")
            
            assert result.status == SubscriptionStatus.CANCELLED
            assert "Test reason" in result.notes
            mock_marzban_client.update_user.assert_called_once()
            mock_db_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_extend_subscription_success(
        self,
        subscription_service,
        sample_subscription,
        mock_db_session,
        mock_marzban_client
    ):
        """Тест успешного продления подписки"""
        original_end_date = sample_subscription.end_date
        original_traffic_limit = sample_subscription.traffic_limit
        
        with patch.object(subscription_service, '_get_subscription_by_id') as mock_get_sub:
            mock_get_sub.return_value = sample_subscription
            mock_marzban_client.update_user = AsyncMock()
            
            result = await subscription_service.extend_subscription(
                subscription_id=1,
                additional_days=30,
                additional_traffic=53687091200  # 50 GB
            )
            
            # Проверяем, что дата продлена
            assert result.end_date > original_end_date
            assert (result.end_date - original_end_date).days == 30
            
            # Проверяем, что трафик увеличен
            assert result.traffic_limit == original_traffic_limit + 53687091200
            
            mock_marzban_client.update_user.assert_called_once()
            mock_db_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_sync_traffic_usage_success(
        self,
        subscription_service,
        sample_subscription,
        mock_db_session,
        mock_marzban_client
    ):
        """Тест успешной синхронизации трафика"""
        # Мок ответа от Marzban
        mock_marzban_user = MagicMock()
        mock_marzban_user.used_traffic = 5368709120  # 5 GB
        
        with patch.object(subscription_service, '_get_subscription_by_id') as mock_get_sub:
            with patch('app.services.subscription_service.create_marzban_username') as mock_create_username:
                mock_get_sub.return_value = sample_subscription
                mock_create_username.return_value = "test_user_123456789"
                mock_marzban_client.get_user = AsyncMock(return_value=mock_marzban_user)
                
                result = await subscription_service.sync_traffic_usage(1)
                
                assert result.traffic_used == 5368709120
                mock_marzban_client.get_user.assert_called_once_with("test_user_123456789")
                mock_db_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_sync_traffic_usage_user_not_found_in_marzban(
        self,
        subscription_service,
        sample_subscription,
        mock_db_session,
        mock_marzban_client
    ):
        """Тест синхронизации трафика когда пользователь не найден в Marzban"""
        with patch.object(subscription_service, '_get_subscription_by_id') as mock_get_sub:
            with patch('app.services.subscription_service.create_marzban_username') as mock_create_username:
                mock_get_sub.return_value = sample_subscription
                mock_create_username.return_value = "test_user_123456789"
                mock_marzban_client.get_user = AsyncMock(
                    side_effect=MarzbanUserNotFoundError("User not found")
                )
                
                # Не должно вызывать исключение
                result = await subscription_service.sync_traffic_usage(1)
                
                assert result == sample_subscription
                # Трафик не должен измениться
                assert result.traffic_used == 0
    
    @pytest.mark.asyncio
    async def test_check_expiring_subscriptions(
        self,
        subscription_service,
        mock_db_session
    ):
        """Тест поиска истекающих подписок"""
        # Мок результата запроса
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [
            MagicMock(id=1, end_date=datetime.now(timezone.utc) + timedelta(days=2)),
            MagicMock(id=2, end_date=datetime.now(timezone.utc) + timedelta(days=1))
        ]
        mock_db_session.execute.return_value = mock_result
        
        result = await subscription_service.check_expiring_subscriptions(days_before=3)
        
        assert len(result) == 2
        mock_db_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_process_expired_subscriptions(
        self,
        subscription_service,
        mock_db_session,
        mock_marzban_client
    ):
        """Тест обработки истекших подписок"""
        # Создаем мок истекшей подписки
        expired_subscription = MagicMock()
        expired_subscription.id = 1
        expired_subscription.status = SubscriptionStatus.ACTIVE
        expired_subscription.end_date = datetime.now(timezone.utc) - timedelta(days=1)
        expired_subscription.user = MagicMock()
        
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [expired_subscription]
        mock_db_session.execute.return_value = mock_result
        
        mock_marzban_client.update_user = AsyncMock()
        
        result = await subscription_service.process_expired_subscriptions()
        
        assert len(result) == 1
        assert expired_subscription.status == SubscriptionStatus.EXPIRED
        mock_marzban_client.update_user.assert_called_once()
        mock_db_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_user_active_subscription(
        self,
        subscription_service,
        sample_user,
        sample_subscription,
        mock_db_session
    ):
        """Тест получения активной подписки пользователя"""
        with patch('app.services.subscription_service.get_user_by_telegram_id') as mock_get_user:
            mock_get_user.return_value = sample_user
            
            # Мок для активных подписок
            mock_result = MagicMock()
            mock_result.scalars.return_value.all.return_value = [sample_subscription]
            mock_db_session.execute.return_value = mock_result
            
            result = await subscription_service.get_user_active_subscription(123456789)
            
            assert result == sample_subscription
            mock_get_user.assert_called_once_with(mock_db_session, 123456789)
    
    @pytest.mark.asyncio
    async def test_get_subscription_info(
        self,
        subscription_service,
        sample_subscription,
        mock_db_session
    ):
        """Тест получения информации о подписке"""
        with patch.object(subscription_service, '_get_subscription_by_id') as mock_get_sub:
            with patch.object(subscription_service, 'sync_traffic_usage') as mock_sync:
                mock_get_sub.return_value = sample_subscription
                mock_sync.return_value = sample_subscription
                
                result = await subscription_service.get_subscription_info(1)
                
                assert result['id'] == sample_subscription.id
                assert result['status'] == sample_subscription.status.value
                assert result['plan_name'] == sample_subscription.plan.name
                assert 'days_remaining' in result
                assert 'traffic_usage_percent' in result
                
                mock_sync.assert_called_once_with(1)
