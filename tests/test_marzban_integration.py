"""
Тесты интеграции с Marzban API
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone, timedelta

from app.integrations.marzban import (
    MarzbanClient,
    MarzbanConnectionConfig,
    MarzbanUserCreate,
    MarzbanUserUpdate,
    MarzbanUserResponse,
    MarzbanUserStatus,
    MarzbanException,
    MarzbanConnectionError,
    MarzbanAuthenticationError,
    MarzbanUserNotFoundError,
    MarzbanUserAlreadyExistsError,
    MarzbanAPIError,
)


class TestMarzbanClient:
    """Тесты клиента Marzban API"""
    
    @pytest.fixture
    def marzban_config(self):
        """Конфигурация для тестов"""
        return MarzbanConnectionConfig(
            base_url="http://test-marzban.local:8000",
            username="test_admin",
            password="test_password",
            timeout=10,
            verify_ssl=False,
            max_retries=2,
            retry_delay=0.1
        )
    
    @pytest.fixture
    def marzban_client(self, marzban_config):
        """Клиент Marzban для тестов"""
        return MarzbanClient(config=marzban_config)
    
    @pytest.fixture
    def sample_user_data(self):
        """Образец данных пользователя"""
        return MarzbanUserCreate(
            username="test_user_123",
            proxies={
                "vmess": {"id": "test-uuid"},
                "vless": {"id": "test-uuid"}
            },
            data_limit=10737418240,  # 10 GB
            expire=int((datetime.now(timezone.utc) + timedelta(days=30)).timestamp()),
            status=MarzbanUserStatus.ACTIVE,
            note="Test user for integration tests"
        )
    
    @pytest.fixture
    def sample_user_response(self):
        """Образец ответа с данными пользователя"""
        return MarzbanUserResponse(
            username="test_user_123",
            proxies={
                "vmess": {"id": "test-uuid"},
                "vless": {"id": "test-uuid"}
            },
            data_limit=10737418240,
            data_limit_reset_strategy="no_reset",
            expire=int((datetime.now(timezone.utc) + timedelta(days=30)).timestamp()),
            status=MarzbanUserStatus.ACTIVE,
            used_traffic=1073741824,  # 1 GB used
            lifetime_used_traffic=1073741824,
            created_at="2024-01-01T00:00:00Z",
            links=["vmess://test-link", "vless://test-link"],
            subscription_url="https://test.local/sub/test_user_123",
            excluded_inbounds={},
            note="Test user for integration tests",
            sub_updated_at=None,
            sub_last_user_agent=None,
            online_at=None,
            on_hold_expire_duration=None,
            on_hold_timeout=None,
            auto_delete_in_days=None
        )
    
    @pytest.mark.asyncio
    async def test_authentication_success(self, marzban_client):
        """Тест успешной аутентификации"""
        mock_token_response = MagicMock()
        mock_token_response.status_code = 200
        mock_token_response.parsed = MagicMock()
        mock_token_response.parsed.access_token = "test_access_token"
        
        with patch('app.integrations.marzban.client.admin_token') as mock_admin_token:
            mock_admin_token.asyncio_detailed = AsyncMock(return_value=mock_token_response)
            
            await marzban_client._authenticate()
            
            assert marzban_client._client is not None
            assert marzban_client._token_expires_at is not None
            assert marzban_client._token_expires_at > datetime.now(timezone.utc)
    
    @pytest.mark.asyncio
    async def test_authentication_failure(self, marzban_client):
        """Тест неудачной аутентификации"""
        mock_token_response = MagicMock()
        mock_token_response.status_code = 401
        
        with patch('app.integrations.marzban.client.admin_token') as mock_admin_token:
            mock_admin_token.asyncio_detailed = AsyncMock(return_value=mock_token_response)
            
            with pytest.raises(MarzbanAuthenticationError):
                await marzban_client._authenticate()
    
    @pytest.mark.asyncio
    async def test_create_user_success(self, marzban_client, sample_user_data, sample_user_response):
        """Тест успешного создания пользователя"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.parsed = MagicMock()
        mock_response.parsed.to_dict.return_value = sample_user_response.model_dump()
        
        with patch.object(marzban_client, '_ensure_authenticated', new_callable=AsyncMock):
            with patch('app.integrations.marzban.client.add_user') as mock_add_user:
                mock_add_user.asyncio_detailed = AsyncMock(return_value=mock_response)
                
                result = await marzban_client.create_user(sample_user_data)
                
                assert isinstance(result, MarzbanUserResponse)
                assert result.username == sample_user_data.username
                assert result.status == sample_user_data.status
    
    @pytest.mark.asyncio
    async def test_create_user_already_exists(self, marzban_client, sample_user_data):
        """Тест создания пользователя, который уже существует"""
        mock_response = MagicMock()
        mock_response.status_code = 409
        
        with patch.object(marzban_client, '_ensure_authenticated', new_callable=AsyncMock):
            with patch('app.integrations.marzban.client.add_user') as mock_add_user:
                mock_add_user.asyncio_detailed = AsyncMock(return_value=mock_response)
                
                with pytest.raises(MarzbanUserAlreadyExistsError):
                    await marzban_client.create_user(sample_user_data)
    
    @pytest.mark.asyncio
    async def test_get_user_success(self, marzban_client, sample_user_response):
        """Тест успешного получения пользователя"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.parsed = MagicMock()
        mock_response.parsed.to_dict.return_value = sample_user_response.model_dump()
        
        with patch.object(marzban_client, '_ensure_authenticated', new_callable=AsyncMock):
            with patch('app.integrations.marzban.client.get_user') as mock_get_user:
                mock_get_user.asyncio_detailed = AsyncMock(return_value=mock_response)
                
                result = await marzban_client.get_user("test_user_123")
                
                assert isinstance(result, MarzbanUserResponse)
                assert result.username == "test_user_123"
    
    @pytest.mark.asyncio
    async def test_get_user_not_found(self, marzban_client):
        """Тест получения несуществующего пользователя"""
        mock_response = MagicMock()
        mock_response.status_code = 404
        
        with patch.object(marzban_client, '_ensure_authenticated', new_callable=AsyncMock):
            with patch('app.integrations.marzban.client.get_user') as mock_get_user:
                mock_get_user.asyncio_detailed = AsyncMock(return_value=mock_response)
                
                with pytest.raises(MarzbanUserNotFoundError):
                    await marzban_client.get_user("nonexistent_user")
    
    @pytest.mark.asyncio
    async def test_update_user_success(self, marzban_client, sample_user_response):
        """Тест успешного обновления пользователя"""
        update_data = MarzbanUserUpdate(
            data_limit=21474836480,  # 20 GB
            status=MarzbanUserStatus.DISABLED,
            note="Updated test user"
        )
        
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.parsed = MagicMock()
        mock_response.parsed.to_dict.return_value = sample_user_response.model_dump()
        
        with patch.object(marzban_client, '_ensure_authenticated', new_callable=AsyncMock):
            with patch('app.integrations.marzban.client.modify_user') as mock_modify_user:
                mock_modify_user.asyncio_detailed = AsyncMock(return_value=mock_response)
                
                result = await marzban_client.update_user("test_user_123", update_data)
                
                assert isinstance(result, MarzbanUserResponse)
                assert result.username == "test_user_123"
    
    @pytest.mark.asyncio
    async def test_delete_user_success(self, marzban_client):
        """Тест успешного удаления пользователя"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        
        with patch.object(marzban_client, '_ensure_authenticated', new_callable=AsyncMock):
            with patch('app.integrations.marzban.client.remove_user') as mock_remove_user:
                mock_remove_user.asyncio_detailed = AsyncMock(return_value=mock_response)
                
                result = await marzban_client.delete_user("test_user_123")
                
                assert result is True
    
    @pytest.mark.asyncio
    async def test_delete_user_not_found(self, marzban_client):
        """Тест удаления несуществующего пользователя"""
        mock_response = MagicMock()
        mock_response.status_code = 404
        
        with patch.object(marzban_client, '_ensure_authenticated', new_callable=AsyncMock):
            with patch('app.integrations.marzban.client.remove_user') as mock_remove_user:
                mock_remove_user.asyncio_detailed = AsyncMock(return_value=mock_response)
                
                with pytest.raises(MarzbanUserNotFoundError):
                    await marzban_client.delete_user("nonexistent_user")
    
    @pytest.mark.asyncio
    async def test_retry_mechanism(self, marzban_client, sample_user_data):
        """Тест механизма повторных попыток"""
        # Первая попытка неудачная, вторая успешная
        error_response = MagicMock()
        error_response.status_code = 500

        success_response = MagicMock()
        success_response.status_code = 200
        success_response.parsed = MagicMock()
        success_response.parsed.to_dict.return_value = {
            "username": "test_user_123",
            "status": "active",
            "data_limit": 10737418240,
            "used_traffic": 0,
            "lifetime_used_traffic": 0,
            "created_at": "2024-01-01T00:00:00Z",
            "proxies": {},
            "data_limit_reset_strategy": "no_reset",
            "expire": None,
            "links": [],
            "excluded_inbounds": {}
        }

        with patch.object(marzban_client, '_ensure_authenticated', new_callable=AsyncMock):
            with patch('app.integrations.marzban.client.add_user') as mock_add_user:
                mock_add_user.asyncio_detailed = AsyncMock(side_effect=[error_response, success_response])

                # Должно успешно выполниться после повторной попытки
                result = await marzban_client.create_user(sample_user_data)

                assert isinstance(result, MarzbanUserResponse)
                assert result.username == "test_user_123"

                # Проверяем, что было сделано 2 попытки
                assert mock_add_user.asyncio_detailed.call_count == 2
    
    @pytest.mark.asyncio
    async def test_context_manager(self, marzban_client):
        """Тест использования клиента как контекстного менеджера"""
        with patch.object(marzban_client, '_ensure_authenticated', new_callable=AsyncMock) as mock_auth:
            with patch.object(marzban_client, 'close', new_callable=AsyncMock) as mock_close:
                
                async with marzban_client as client:
                    assert client is marzban_client
                    mock_auth.assert_called_once()
                
                mock_close.assert_called_once()


class TestMarzbanUserResponse:
    """Тесты модели ответа пользователя Marzban"""
    
    def test_expire_datetime_property(self):
        """Тест свойства expire_datetime"""
        expire_timestamp = int((datetime.now(timezone.utc) + timedelta(days=30)).timestamp())
        
        user = MarzbanUserResponse(
            username="test_user",
            proxies={},
            data_limit=None,
            data_limit_reset_strategy="no_reset",
            expire=expire_timestamp,
            status=MarzbanUserStatus.ACTIVE,
            used_traffic=0,
            lifetime_used_traffic=0,
            created_at="2024-01-01T00:00:00Z",
            links=[],
            excluded_inbounds={}
        )
        
        assert user.expire_datetime is not None
        assert isinstance(user.expire_datetime, datetime)
        assert user.expire_datetime.tzinfo == timezone.utc
    
    def test_is_expired_property(self):
        """Тест свойства is_expired"""
        # Пользователь с истекшим сроком
        expired_user = MarzbanUserResponse(
            username="expired_user",
            proxies={},
            data_limit=None,
            data_limit_reset_strategy="no_reset",
            expire=int((datetime.now(timezone.utc) - timedelta(days=1)).timestamp()),
            status=MarzbanUserStatus.EXPIRED,
            used_traffic=0,
            lifetime_used_traffic=0,
            created_at="2024-01-01T00:00:00Z",
            links=[],
            excluded_inbounds={}
        )
        
        assert expired_user.is_expired is True
        
        # Пользователь с действующим сроком
        active_user = MarzbanUserResponse(
            username="active_user",
            proxies={},
            data_limit=None,
            data_limit_reset_strategy="no_reset",
            expire=int((datetime.now(timezone.utc) + timedelta(days=30)).timestamp()),
            status=MarzbanUserStatus.ACTIVE,
            used_traffic=0,
            lifetime_used_traffic=0,
            created_at="2024-01-01T00:00:00Z",
            links=[],
            excluded_inbounds={}
        )
        
        assert active_user.is_expired is False
    
    def test_traffic_properties(self):
        """Тест свойств трафика"""
        user = MarzbanUserResponse(
            username="test_user",
            proxies={},
            data_limit=10737418240,  # 10 GB
            data_limit_reset_strategy="no_reset",
            expire=None,
            status=MarzbanUserStatus.ACTIVE,
            used_traffic=5368709120,  # 5 GB used
            lifetime_used_traffic=5368709120,
            created_at="2024-01-01T00:00:00Z",
            links=[],
            excluded_inbounds={}
        )
        
        # Тест оставшегося трафика
        assert user.remaining_traffic == 5368709120  # 5 GB remaining
        
        # Тест процента использования
        assert user.traffic_usage_percent == 50.0
        
        # Тест превышения квоты
        assert user.is_quota_exceeded is False
        
        # Пользователь с превышенной квотой
        user.used_traffic = 12884901888  # 12 GB used (more than 10 GB limit)
        assert user.is_quota_exceeded is True
        assert user.remaining_traffic == 0
        assert user.traffic_usage_percent > 100.0  # Может превышать 100%
