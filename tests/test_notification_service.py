"""
Тесты сервиса уведомлений
"""

import pytest
from unittest.mock import AsyncMock, MagicMock
from datetime import datetime, timezone, timedelta
from aiogram.exceptions import TelegramForbiddenError, TelegramBadRequest

from app.services.notification_service import NotificationService, NotificationType
from app.database.models import User, SubscriptionPlan, UserSubscription, SubscriptionStatus


class TestNotificationService:
    """Тесты сервиса уведомлений"""
    
    @pytest.fixture
    def mock_bot(self):
        """Мок Telegram бота"""
        bot = AsyncMock()
        bot.send_message = AsyncMock()
        return bot
    
    @pytest.fixture
    def notification_service(self, mock_bot):
        """Сервис уведомлений для тестов"""
        return NotificationService(bot=mock_bot)
    
    @pytest.fixture
    def sample_user(self):
        """Образец пользователя"""
        user = User()
        user.id = 1
        user.telegram_id = 123456789
        user.username = "testuser"
        user.first_name = "Test"
        user.last_name = "User"
        return user
    
    @pytest.fixture
    def sample_plan(self):
        """Образец тарифного плана"""
        plan = SubscriptionPlan()
        plan.id = 1
        plan.name = "Базовый"
        plan.description = "Базовый тарифный план"
        plan.price = 299.0
        plan.currency = "RUB"
        plan.duration_days = 30
        plan.traffic_limit = 107374182400  # 100 GB
        return plan
    
    @pytest.fixture
    def sample_subscription(self, sample_user, sample_plan):
        """Образец подписки"""
        subscription = UserSubscription()
        subscription.id = 1
        subscription.user_id = sample_user.id
        subscription.plan_id = sample_plan.id
        subscription.status = SubscriptionStatus.ACTIVE
        subscription.start_date = datetime.now(timezone.utc)
        subscription.end_date = datetime.now(timezone.utc) + timedelta(days=30)
        subscription.traffic_limit = sample_plan.traffic_limit
        subscription.traffic_used = 0
        subscription.user = sample_user
        subscription.plan = sample_plan
        return subscription
    
    @pytest.mark.asyncio
    async def test_send_subscription_created_success(
        self,
        notification_service,
        sample_user,
        sample_subscription,
        mock_bot
    ):
        """Тест успешной отправки уведомления о создании подписки"""
        mock_bot.send_message.return_value = MagicMock()
        
        result = await notification_service.send_subscription_created(
            sample_user, 
            sample_subscription
        )
        
        assert result is True
        mock_bot.send_message.assert_called_once()
        
        # Проверяем параметры вызова
        call_args = mock_bot.send_message.call_args
        assert call_args[1]['chat_id'] == sample_user.telegram_id
        assert call_args[1]['parse_mode'] == "HTML"
        assert "Подписка создана!" in call_args[1]['text']
        assert sample_subscription.plan.name in call_args[1]['text']
    
    @pytest.mark.asyncio
    async def test_send_subscription_activated_success(
        self,
        notification_service,
        sample_user,
        sample_subscription,
        mock_bot
    ):
        """Тест успешной отправки уведомления об активации подписки"""
        mock_bot.send_message.return_value = MagicMock()
        
        result = await notification_service.send_subscription_activated(
            sample_user,
            sample_subscription
        )
        
        assert result is True
        mock_bot.send_message.assert_called_once()
        
        call_args = mock_bot.send_message.call_args
        assert "Подписка активирована!" in call_args[1]['text']
        assert "/config" in call_args[1]['text']
        assert "/status" in call_args[1]['text']
    
    @pytest.mark.asyncio
    async def test_send_subscription_expiring_success(
        self,
        notification_service,
        sample_user,
        sample_subscription,
        mock_bot
    ):
        """Тест успешной отправки уведомления о скором истечении подписки"""
        mock_bot.send_message.return_value = MagicMock()
        
        result = await notification_service.send_subscription_expiring(
            sample_user,
            sample_subscription,
            days_left=3
        )
        
        assert result is True
        mock_bot.send_message.assert_called_once()
        
        call_args = mock_bot.send_message.call_args
        assert "Подписка скоро истечет!" in call_args[1]['text']
        assert "3 дня" in call_args[1]['text']
        assert "/renew" in call_args[1]['text']
    
    @pytest.mark.asyncio
    async def test_send_subscription_expired_success(
        self,
        notification_service,
        sample_user,
        sample_subscription,
        mock_bot
    ):
        """Тест успешной отправки уведомления об истечении подписки"""
        mock_bot.send_message.return_value = MagicMock()
        
        result = await notification_service.send_subscription_expired(
            sample_user,
            sample_subscription
        )
        
        assert result is True
        mock_bot.send_message.assert_called_once()
        
        call_args = mock_bot.send_message.call_args
        assert "Подписка истекла" in call_args[1]['text']
        assert "/renew" in call_args[1]['text']
        assert "/plans" in call_args[1]['text']
    
    @pytest.mark.asyncio
    async def test_send_traffic_warning_success(
        self,
        notification_service,
        sample_user,
        sample_subscription,
        mock_bot
    ):
        """Тест успешной отправки предупреждения о трафике"""
        mock_bot.send_message.return_value = MagicMock()
        
        result = await notification_service.send_traffic_warning(
            sample_user,
            sample_subscription,
            usage_percent=85.5
        )
        
        assert result is True
        mock_bot.send_message.assert_called_once()
        
        call_args = mock_bot.send_message.call_args
        assert "Предупреждение о трафике" in call_args[1]['text']
        assert "85.5%" in call_args[1]['text']
        assert "/upgrade" in call_args[1]['text']
    
    @pytest.mark.asyncio
    async def test_send_traffic_exceeded_success(
        self,
        notification_service,
        sample_user,
        sample_subscription,
        mock_bot
    ):
        """Тест успешной отправки уведомления о превышении лимита трафика"""
        mock_bot.send_message.return_value = MagicMock()
        
        result = await notification_service.send_traffic_exceeded(
            sample_user,
            sample_subscription
        )
        
        assert result is True
        mock_bot.send_message.assert_called_once()
        
        call_args = mock_bot.send_message.call_args
        assert "Лимит трафика превышен" in call_args[1]['text']
        assert "/upgrade" in call_args[1]['text']
        assert "/renew" in call_args[1]['text']
    
    @pytest.mark.asyncio
    async def test_send_payment_received_success(
        self,
        notification_service,
        sample_user,
        mock_bot
    ):
        """Тест успешной отправки уведомления о получении платежа"""
        mock_bot.send_message.return_value = MagicMock()
        
        result = await notification_service.send_payment_received(
            sample_user,
            amount=299.0,
            currency="RUB"
        )
        
        assert result is True
        mock_bot.send_message.assert_called_once()
        
        call_args = mock_bot.send_message.call_args
        assert "Платеж получен!" in call_args[1]['text']
        assert "299.00 RUB" in call_args[1]['text']
        assert "/status" in call_args[1]['text']
    
    @pytest.mark.asyncio
    async def test_send_notification_user_blocked_bot(
        self,
        notification_service,
        sample_user,
        sample_subscription,
        mock_bot
    ):
        """Тест отправки уведомления пользователю, который заблокировал бота"""
        mock_bot.send_message.side_effect = TelegramForbiddenError(
            method="sendMessage",
            message="Forbidden: bot was blocked by the user"
        )

        # Теперь ожидаем исключение
        with pytest.raises(TelegramForbiddenError):
            await notification_service.send_subscription_created(
                sample_user,
                sample_subscription
            )

        mock_bot.send_message.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_send_notification_bad_request(
        self,
        notification_service,
        sample_user,
        sample_subscription,
        mock_bot
    ):
        """Тест отправки уведомления с некорректными данными"""
        mock_bot.send_message.side_effect = TelegramBadRequest(
            method="sendMessage",
            message="Bad Request: chat not found"
        )
        
        result = await notification_service.send_subscription_created(
            sample_user,
            sample_subscription
        )
        
        assert result is False
        mock_bot.send_message.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_send_notification_unexpected_error(
        self,
        notification_service,
        sample_user,
        sample_subscription,
        mock_bot
    ):
        """Тест отправки уведомления с неожиданной ошибкой"""
        mock_bot.send_message.side_effect = Exception("Unexpected error")
        
        result = await notification_service.send_subscription_created(
            sample_user,
            sample_subscription
        )
        
        assert result is False
        mock_bot.send_message.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_send_notification_no_bot(self, sample_user, sample_subscription):
        """Тест отправки уведомления без инициализированного бота"""
        notification_service = NotificationService(bot=None)
        
        result = await notification_service.send_subscription_created(
            sample_user,
            sample_subscription
        )
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_send_bulk_notifications_success(
        self,
        notification_service,
        sample_user,
        sample_subscription,
        mock_bot
    ):
        """Тест массовой отправки уведомлений"""
        mock_bot.send_message.return_value = MagicMock()
        
        # Создаем список пользователей и сообщений
        users_and_messages = [
            (sample_user, "Test message 1", NotificationType.SUBSCRIPTION_CREATED),
            (sample_user, "Test message 2", NotificationType.SUBSCRIPTION_ACTIVATED),
        ]
        
        result = await notification_service.send_bulk_notifications(users_and_messages)
        
        assert result["sent"] == 2
        assert result["failed"] == 0
        assert result["blocked"] == 0
        assert result["errors"] == 0
        assert mock_bot.send_message.call_count == 2
    
    @pytest.mark.asyncio
    async def test_send_bulk_notifications_mixed_results(
        self,
        notification_service,
        sample_user,
        mock_bot
    ):
        """Тест массовой отправки уведомлений со смешанными результатами"""
        # Первое сообщение успешно, второе - пользователь заблокировал бота
        mock_bot.send_message.side_effect = [
            MagicMock(),  # Успешно
            TelegramForbiddenError("sendMessage", "bot was blocked")  # Заблокирован
        ]
        
        users_and_messages = [
            (sample_user, "Test message 1", NotificationType.SUBSCRIPTION_CREATED),
            (sample_user, "Test message 2", NotificationType.SUBSCRIPTION_ACTIVATED),
        ]
        
        result = await notification_service.send_bulk_notifications(users_and_messages)
        
        assert result["sent"] == 1
        assert result["failed"] == 0
        assert result["blocked"] == 1
        assert result["errors"] == 0
        assert mock_bot.send_message.call_count == 2
    
    def test_format_subscription_created_message(
        self,
        notification_service,
        sample_user,
        sample_subscription
    ):
        """Тест форматирования сообщения о создании подписки"""
        message = notification_service._format_subscription_created_message(
            sample_subscription
        )
        
        assert "Подписка создана!" in message
        assert sample_subscription.plan.name in message
        assert "100.0 GB" in message  # Форматированный лимит трафика
        assert "/config" in message
    
    def test_format_subscription_expiring_message_days_text(
        self,
        notification_service,
        sample_user,
        sample_subscription
    ):
        """Тест правильного склонения дней в сообщении об истечении"""
        # 1 день
        message1 = notification_service._format_subscription_expiring_message(
            sample_user, sample_subscription, 1
        )
        assert "1 день" in message1
        
        # 2 дня
        message2 = notification_service._format_subscription_expiring_message(
            sample_user, sample_subscription, 2
        )
        assert "2 дня" in message2
        
        # 5 дней
        message5 = notification_service._format_subscription_expiring_message(
            sample_user, sample_subscription, 5
        )
        assert "5 дней" in message5
    
    def test_format_traffic_warning_message(
        self,
        notification_service,
        sample_user,
        sample_subscription
    ):
        """Тест форматирования предупреждения о трафике"""
        # Устанавливаем использованный трафик
        sample_subscription.traffic_used = 85899345920  # 80 GB
        
        message = notification_service._format_traffic_warning_message(
            sample_user,
            sample_subscription,
            80.0
        )
        
        assert "Предупреждение о трафике" in message
        assert "80.0 GB" in message  # Использованный трафик
        assert "100.0 GB" in message  # Лимит трафика
        assert "80.0%" in message  # Процент использования
        assert "/upgrade" in message
    
    def test_format_payment_received_message(
        self,
        notification_service,
        sample_user
    ):
        """Тест форматирования сообщения о получении платежа"""
        message = notification_service._format_payment_received_message(
            sample_user,
            299.50,
            "RUB"
        )
        
        assert "Платеж получен!" in message
        assert "299.50 RUB" in message
        assert "/status" in message
