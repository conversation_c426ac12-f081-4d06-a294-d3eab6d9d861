"""
Тесты для управления пользователями в админ-панели
"""

import pytest
from unittest.mock import AsyncMock, patch
from fastapi.testclient import TestClient
from datetime import datetime, timezone

from app.main import app
from app.admin.models import AdminUser, AdminRole, AdminPermission
from app.database.models import User, UserSubscription, SubscriptionStatus
from app.admin.schemas.users import UserCreate, UserUpdate


@pytest.fixture
def mock_admin_user():
    """Фикстура с администратором для тестов"""
    return AdminUser(
        id=1,
        username="test_admin",
        email="<EMAIL>",
        role=AdminRole.ADMIN,
        is_active=True,
        is_superuser=False,
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc)
    )


@pytest.fixture
def mock_user():
    """Фикстура с обычным пользователем"""
    return User(
        id=1,
        telegram_id=123456789,
        username="test_user",
        email="<EMAIL>",
        full_name="Test User",
        language_code="ru",
        is_active=True,
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc)
    )


@pytest.fixture
def auth_headers():
    """Фикстура с заголовками аутентификации"""
    return {"Authorization": "Bearer test_token"}


class TestUsersCRUD:
    """Тесты для CRUD операций с пользователями"""
    
    def test_list_users_success(self, mock_admin_user, mock_user, auth_headers):
        """Тест получения списка пользователей"""
        client = TestClient(app)
        
        with patch('app.admin.routers.users.get_current_admin_user') as mock_auth, \
             patch('app.admin.routers.users.get_admin_db') as mock_db:
            
            # Мокаем аутентификацию
            mock_auth.return_value = mock_admin_user
            
            # Мокаем БД
            mock_session = AsyncMock()
            mock_db.return_value = mock_session
            
            # Мокаем результат запроса пользователей
            mock_result = AsyncMock()
            mock_result.scalars.return_value.all.return_value = [mock_user]
            mock_session.execute.return_value = mock_result
            
            # Мокаем подсчет общего количества
            mock_session.scalar.return_value = 1
            
            response = client.get(
                "/admin/api/users/",
                headers=auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "users" in data
            assert "pagination" in data
            assert len(data["users"]) >= 0
    
    def test_list_users_with_filters(self, mock_admin_user, auth_headers):
        """Тест получения списка пользователей с фильтрами"""
        client = TestClient(app)
        
        with patch('app.admin.routers.users.get_current_admin_user') as mock_auth, \
             patch('app.admin.routers.users.get_admin_db') as mock_db:
            
            mock_auth.return_value = mock_admin_user
            mock_session = AsyncMock()
            mock_db.return_value = mock_session
            
            # Мокаем пустой результат
            mock_result = AsyncMock()
            mock_result.scalars.return_value.all.return_value = []
            mock_session.execute.return_value = mock_result
            mock_session.scalar.return_value = 0
            
            response = client.get(
                "/admin/api/users/?search=test&status_filter=active&page=1&per_page=10",
                headers=auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["users"] == []
            assert data["pagination"]["page"] == 1
            assert data["pagination"]["per_page"] == 10
    
    def test_get_user_stats(self, mock_admin_user, auth_headers):
        """Тест получения статистики пользователей"""
        client = TestClient(app)
        
        with patch('app.admin.routers.users.get_current_admin_user') as mock_auth, \
             patch('app.admin.routers.users.get_admin_db') as mock_db:
            
            mock_auth.return_value = mock_admin_user
            mock_session = AsyncMock()
            mock_db.return_value = mock_session
            
            # Мокаем статистику
            mock_session.scalar.side_effect = [100, 80, 50, 25]  # total, active, with_subs, new
            
            response = client.get(
                "/admin/api/users/stats",
                headers=auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["total_users"] == 100
            assert data["active_users"] == 80
            assert data["inactive_users"] == 20
            assert data["users_with_active_subscriptions"] == 50
            assert data["new_users_last_30_days"] == 25
    
    def test_get_user_detail(self, mock_admin_user, mock_user, auth_headers):
        """Тест получения детальной информации о пользователе"""
        client = TestClient(app)
        
        with patch('app.admin.routers.users.get_current_admin_user') as mock_auth, \
             patch('app.admin.routers.users.get_admin_db') as mock_db:
            
            mock_auth.return_value = mock_admin_user
            mock_session = AsyncMock()
            mock_db.return_value = mock_session
            
            # Мокаем результат запроса пользователя
            mock_result = AsyncMock()
            mock_result.scalar_one_or_none.return_value = mock_user
            mock_session.execute.return_value = mock_result
            
            response = client.get(
                "/admin/api/users/1",
                headers=auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["id"] == 1
            assert data["username"] == "test_user"
            assert data["telegram_id"] == 123456789
    
    def test_get_user_not_found(self, mock_admin_user, auth_headers):
        """Тест получения несуществующего пользователя"""
        client = TestClient(app)
        
        with patch('app.admin.routers.users.get_current_admin_user') as mock_auth, \
             patch('app.admin.routers.users.get_admin_db') as mock_db:
            
            mock_auth.return_value = mock_admin_user
            mock_session = AsyncMock()
            mock_db.return_value = mock_session
            
            # Мокаем отсутствие пользователя
            mock_result = AsyncMock()
            mock_result.scalar_one_or_none.return_value = None
            mock_session.execute.return_value = mock_result
            
            response = client.get(
                "/admin/api/users/999",
                headers=auth_headers
            )
            
            assert response.status_code == 404
            assert "не найден" in response.json()["detail"]
    
    def test_create_user_success(self, mock_admin_user, auth_headers):
        """Тест успешного создания пользователя"""
        client = TestClient(app)
        
        with patch('app.admin.routers.users.get_current_admin_user') as mock_auth, \
             patch('app.admin.routers.users.get_admin_db') as mock_db:
            
            mock_auth.return_value = mock_admin_user
            mock_session = AsyncMock()
            mock_db.return_value = mock_session
            
            # Мокаем отсутствие существующего пользователя
            mock_result = AsyncMock()
            mock_result.scalar.return_value = None
            mock_session.execute.return_value = mock_result
            
            # Мокаем созданного пользователя
            created_user = User(
                id=2,
                telegram_id=987654321,
                username="new_user",
                email="<EMAIL>",
                full_name="New User",
                language_code="en",
                is_active=True,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
            mock_session.refresh = AsyncMock()
            mock_session.refresh.side_effect = lambda obj: setattr(obj, 'id', 2)
            
            user_data = {
                "telegram_id": 987654321,
                "username": "new_user",
                "email": "<EMAIL>",
                "full_name": "New User",
                "language_code": "en",
                "is_active": True
            }
            
            response = client.post(
                "/admin/api/users/",
                json=user_data,
                headers=auth_headers
            )
            
            assert response.status_code == 201
            data = response.json()
            assert data["username"] == "new_user"
            assert data["telegram_id"] == 987654321
    
    def test_create_user_duplicate_username(self, mock_admin_user, mock_user, auth_headers):
        """Тест создания пользователя с существующим username"""
        client = TestClient(app)
        
        with patch('app.admin.routers.users.get_current_admin_user') as mock_auth, \
             patch('app.admin.routers.users.get_admin_db') as mock_db:
            
            mock_auth.return_value = mock_admin_user
            mock_session = AsyncMock()
            mock_db.return_value = mock_session
            
            # Мокаем существующего пользователя
            mock_result = AsyncMock()
            mock_result.scalar.return_value = mock_user
            mock_session.execute.return_value = mock_result
            
            user_data = {
                "telegram_id": 987654321,
                "username": "test_user",  # Уже существует
                "email": "<EMAIL>",
                "is_active": True
            }
            
            response = client.post(
                "/admin/api/users/",
                json=user_data,
                headers=auth_headers
            )
            
            assert response.status_code == 400
            assert "уже существует" in response.json()["detail"]
    
    def test_update_user_success(self, mock_admin_user, mock_user, auth_headers):
        """Тест успешного обновления пользователя"""
        client = TestClient(app)
        
        with patch('app.admin.routers.users.get_current_admin_user') as mock_auth, \
             patch('app.admin.routers.users.get_admin_db') as mock_db:
            
            mock_auth.return_value = mock_admin_user
            mock_session = AsyncMock()
            mock_db.return_value = mock_session
            
            # Мокаем получение пользователя
            mock_result = AsyncMock()
            mock_result.scalar.return_value = mock_user
            mock_session.execute.return_value = mock_result
            
            update_data = {
                "full_name": "Updated User Name",
                "is_active": False
            }
            
            response = client.put(
                "/admin/api/users/1",
                json=update_data,
                headers=auth_headers
            )
            
            assert response.status_code == 200
            # Проверяем, что данные были обновлены
            assert mock_user.full_name == "Updated User Name"
            assert mock_user.is_active is False
    
    def test_delete_user_success(self, mock_admin_user, mock_user, auth_headers):
        """Тест успешного удаления пользователя"""
        client = TestClient(app)
        
        with patch('app.admin.routers.users.get_current_admin_user') as mock_auth, \
             patch('app.admin.routers.users.get_admin_db') as mock_db:
            
            mock_auth.return_value = mock_admin_user
            mock_session = AsyncMock()
            mock_db.return_value = mock_session
            
            # Мокаем получение пользователя
            mock_result = AsyncMock()
            mock_result.scalar.side_effect = [mock_user, None]  # user exists, no active subscription
            mock_session.execute.return_value = mock_result
            
            response = client.delete(
                "/admin/api/users/1",
                headers=auth_headers
            )
            
            assert response.status_code == 204
    
    def test_delete_user_with_active_subscription(self, mock_admin_user, mock_user, auth_headers):
        """Тест удаления пользователя с активной подпиской"""
        client = TestClient(app)
        
        with patch('app.admin.routers.users.get_current_admin_user') as mock_auth, \
             patch('app.admin.routers.users.get_admin_db') as mock_db:
            
            mock_auth.return_value = mock_admin_user
            mock_session = AsyncMock()
            mock_db.return_value = mock_session
            
            # Мокаем пользователя с активной подпиской
            active_subscription = UserSubscription(
                id=1,
                user_id=1,
                status=SubscriptionStatus.ACTIVE
            )
            
            mock_result = AsyncMock()
            mock_result.scalar.side_effect = [mock_user, active_subscription]
            mock_session.execute.return_value = mock_result
            
            response = client.delete(
                "/admin/api/users/1",
                headers=auth_headers
            )
            
            assert response.status_code == 400
            assert "активной подпиской" in response.json()["detail"]
    
    def test_activate_user(self, mock_admin_user, mock_user, auth_headers):
        """Тест активации пользователя"""
        client = TestClient(app)
        
        with patch('app.admin.routers.users.get_current_admin_user') as mock_auth, \
             patch('app.admin.routers.users.get_admin_db') as mock_db:
            
            mock_auth.return_value = mock_admin_user
            mock_session = AsyncMock()
            mock_db.return_value = mock_session
            
            # Деактивируем пользователя для теста
            mock_user.is_active = False
            
            mock_result = AsyncMock()
            mock_result.scalar.return_value = mock_user
            mock_session.execute.return_value = mock_result
            
            response = client.post(
                "/admin/api/users/1/activate",
                headers=auth_headers
            )
            
            assert response.status_code == 200
            assert mock_user.is_active is True
    
    def test_deactivate_user(self, mock_admin_user, mock_user, auth_headers):
        """Тест деактивации пользователя"""
        client = TestClient(app)
        
        with patch('app.admin.routers.users.get_current_admin_user') as mock_auth, \
             patch('app.admin.routers.users.get_admin_db') as mock_db:
            
            mock_auth.return_value = mock_admin_user
            mock_session = AsyncMock()
            mock_db.return_value = mock_session
            
            mock_result = AsyncMock()
            mock_result.scalar.return_value = mock_user
            mock_session.execute.return_value = mock_result
            
            response = client.post(
                "/admin/api/users/1/deactivate",
                headers=auth_headers
            )
            
            assert response.status_code == 200
            assert mock_user.is_active is False


class TestUserPermissions:
    """Тесты для проверки разрешений при работе с пользователями"""
    
    def test_access_denied_without_permission(self, auth_headers):
        """Тест отказа в доступе без соответствующих разрешений"""
        client = TestClient(app)
        
        # Создаем администратора без разрешений на просмотр пользователей
        viewer_admin = AdminUser(
            id=1,
            username="viewer",
            email="<EMAIL>",
            role=AdminRole.VIEWER,
            is_active=True,
            is_superuser=False
        )
        
        # Переопределяем метод has_permission для отказа в доступе
        viewer_admin.has_permission = lambda perm: False
        
        with patch('app.admin.routers.users.get_current_admin_user') as mock_auth:
            mock_auth.return_value = viewer_admin
            
            response = client.get(
                "/admin/api/users/",
                headers=auth_headers
            )
            
            # Должен быть отказ в доступе, но поскольку мы мокаем dependency,
            # нужно проверить, что dependency вызывается
            # В реальном тесте это будет 403
            assert response.status_code in [403, 422]  # 422 если dependency не прошел
    
    def test_superuser_access(self, auth_headers):
        """Тест доступа суперпользователя ко всем операциям"""
        client = TestClient(app)
        
        superuser = AdminUser(
            id=1,
            username="superuser",
            email="<EMAIL>",
            role=AdminRole.SUPER_ADMIN,
            is_active=True,
            is_superuser=True
        )
        
        with patch('app.admin.routers.users.get_current_admin_user') as mock_auth, \
             patch('app.admin.routers.users.get_admin_db') as mock_db:
            
            mock_auth.return_value = superuser
            mock_session = AsyncMock()
            mock_db.return_value = mock_session
            
            # Мокаем пустой результат
            mock_result = AsyncMock()
            mock_result.scalars.return_value.all.return_value = []
            mock_session.execute.return_value = mock_result
            mock_session.scalar.return_value = 0
            
            response = client.get(
                "/admin/api/users/",
                headers=auth_headers
            )
            
            # Суперпользователь должен иметь доступ
            assert response.status_code == 200
