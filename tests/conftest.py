"""
Конфигурация тестов
"""

import pytest
import asyncio
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import StaticPool
import os
import sys

# Добавляем корневую директорию в путь
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database.database import Base, get_db
from app.database.models import User, SubscriptionPlan
from app.core.config import settings


# Тестовая база данных в памяти
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"

# Создаем тестовый движок
test_engine = create_async_engine(
    TEST_DATABASE_URL,
    poolclass=StaticPool,
    connect_args={"check_same_thread": False},
    echo=False
)

# Фабрика тестовых сессий
TestSessionLocal = async_sessionmaker(
    test_engine,
    class_=AsyncSession,
    expire_on_commit=False
)


@pytest.fixture(scope="session")
def event_loop():
    """Создание event loop для тестов"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
async def db_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Создание тестовой сессии базы данных

    Returns:
        AsyncSession: Тестовая сессия
    """
    # Создаем таблицы
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    # Создаем сессию
    async with TestSessionLocal() as session:
        yield session

    # Удаляем таблицы после теста
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


@pytest.fixture
async def sample_user(db_session: AsyncSession) -> User:
    """
    Создание тестового пользователя
    
    Args:
        db_session: Сессия базы данных
        
    Returns:
        User: Тестовый пользователь
    """
    user = User(
        telegram_id=123456789,
        username="testuser",
        first_name="Test",
        last_name="User",
        language_code="ru",
        referral_code="TEST1234"
    )
    
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    
    return user


@pytest.fixture
async def sample_plan(db_session: AsyncSession) -> SubscriptionPlan:
    """
    Создание тестового тарифного плана
    
    Args:
        db_session: Сессия базы данных
        
    Returns:
        SubscriptionPlan: Тестовый план
    """
    plan = SubscriptionPlan(
        name="Тестовый план",
        description="Тестовое описание",
        price=299.00,
        duration_days=30,
        traffic_limit_gb=100,
        max_devices=1,
        is_active=True
    )
    
    db_session.add(plan)
    await db_session.commit()
    await db_session.refresh(plan)
    
    return plan


@pytest.fixture
def sample_user_data():
    """
    Тестовые данные пользователя
    
    Returns:
        dict: Данные пользователя
    """
    return {
        'telegram_id': 987654321,
        'username': 'newuser',
        'first_name': 'New',
        'last_name': 'User',
        'language_code': 'en'
    }


# Переопределяем зависимость get_db для тестов
@pytest.fixture(autouse=True)
async def override_db_dependency(db_session):
    """Переопределение зависимости базы данных для тестов"""
    import app.database.crud

    # Получаем сессию из генератора
    session = None
    async for s in db_session:
        session = s
        break

    async def override_get_db():
        yield session

    # Заменяем функцию get_db на тестовую версию
    original_get_db = app.database.crud.get_db
    app.database.crud.get_db = override_get_db

    yield session

    # Восстанавливаем оригинальную функцию
    app.database.crud.get_db = original_get_db
