"""
Тесты для аутентификации админ-панели
"""

import pytest
from datetime import datetime, timezone, timedelta
from unittest.mock import AsyncMock, patch
from fastapi import HTTPException
from fastapi.testclient import TestClient

from app.admin.auth import AdminAuth, get_current_admin_user
from app.admin.models import AdminUser, AdminRole, AdminSession
from app.admin.schemas.auth import AdminLoginRequest, AdminTokenResponse
from app.main import app


@pytest.fixture
def admin_user_data():
    """Фикстура с данными администратора"""
    return {
        "username": "test_admin",
        "email": "<EMAIL>",
        "password": "TestPassword123",
        "full_name": "Test Administrator",
        "role": AdminRole.ADMIN,
        "is_active": True,
        "is_superuser": False
    }


@pytest.fixture
def mock_admin_user(admin_user_data):
    """Фикстура с мок-объектом администратора"""
    admin = AdminUser(
        id=1,
        username=admin_user_data["username"],
        email=admin_user_data["email"],
        hashed_password=AdminAuth.get_password_hash(admin_user_data["password"]),
        full_name=admin_user_data["full_name"],
        role=admin_user_data["role"],
        is_active=admin_user_data["is_active"],
        is_superuser=admin_user_data["is_superuser"],
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc)
    )
    return admin


class TestAdminAuth:
    """Тесты для класса AdminAuth"""
    
    def test_password_hashing(self):
        """Тест хеширования и проверки паролей"""
        password = "TestPassword123"
        
        # Хешируем пароль
        hashed = AdminAuth.get_password_hash(password)
        assert hashed != password
        assert len(hashed) > 50  # Bcrypt хеши довольно длинные
        
        # Проверяем правильный пароль
        assert AdminAuth.verify_password(password, hashed) is True
        
        # Проверяем неправильный пароль
        assert AdminAuth.verify_password("wrong_password", hashed) is False
    
    def test_create_access_token(self):
        """Тест создания access токена"""
        data = {"sub": 1, "username": "test_admin", "role": "admin"}
        
        token = AdminAuth.create_access_token(data)
        assert isinstance(token, str)
        assert len(token) > 50
        
        # Проверяем токен с кастомным временем жизни
        custom_expire = timedelta(minutes=30)
        token_custom = AdminAuth.create_access_token(data, expires_delta=custom_expire)
        assert isinstance(token_custom, str)
        assert token != token_custom  # Должны быть разными
    
    def test_create_refresh_token(self):
        """Тест создания refresh токена"""
        data = {"sub": 1, "username": "test_admin"}
        
        token = AdminAuth.create_refresh_token(data)
        assert isinstance(token, str)
        assert len(token) > 50
    
    def test_verify_token_valid(self):
        """Тест проверки валидного токена"""
        data = {"sub": 1, "username": "test_admin", "role": "admin"}
        
        # Создаем и проверяем access токен
        access_token = AdminAuth.create_access_token(data)
        payload = AdminAuth.verify_token(access_token, token_type="access")
        
        assert payload is not None
        assert payload["sub"] == 1
        assert payload["username"] == "test_admin"
        assert payload["role"] == "admin"
        assert payload["type"] == "access"
        
        # Создаем и проверяем refresh токен
        refresh_token = AdminAuth.create_refresh_token(data)
        payload = AdminAuth.verify_token(refresh_token, token_type="refresh")
        
        assert payload is not None
        assert payload["sub"] == 1
        assert payload["type"] == "refresh"
    
    def test_verify_token_invalid(self):
        """Тест проверки невалидного токена"""
        # Неправильный токен
        assert AdminAuth.verify_token("invalid_token") is None
        
        # Токен неправильного типа
        data = {"sub": 1, "username": "test_admin"}
        refresh_token = AdminAuth.create_refresh_token(data)
        assert AdminAuth.verify_token(refresh_token, token_type="access") is None
    
    @pytest.mark.asyncio
    async def test_authenticate_admin_success(self, mock_admin_user):
        """Тест успешной аутентификации администратора"""
        mock_db = AsyncMock()
        
        # Мокаем результат запроса к БД
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = mock_admin_user
        mock_db.execute.return_value = mock_result
        
        # Тестируем аутентификацию
        admin = await AdminAuth.authenticate_admin(
            mock_db, "test_admin", "TestPassword123"
        )
        
        assert admin is not None
        assert admin.username == "test_admin"
        assert admin.is_active is True
    
    @pytest.mark.asyncio
    async def test_authenticate_admin_wrong_password(self, mock_admin_user):
        """Тест аутентификации с неправильным паролем"""
        mock_db = AsyncMock()
        
        # Мокаем результат запроса к БД
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = mock_admin_user
        mock_db.execute.return_value = mock_result
        
        # Тестируем аутентификацию с неправильным паролем
        admin = await AdminAuth.authenticate_admin(
            mock_db, "test_admin", "wrong_password"
        )
        
        assert admin is None
    
    @pytest.mark.asyncio
    async def test_authenticate_admin_not_found(self):
        """Тест аутентификации несуществующего администратора"""
        mock_db = AsyncMock()
        
        # Мокаем пустой результат запроса к БД
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db.execute.return_value = mock_result
        
        # Тестируем аутентификацию
        admin = await AdminAuth.authenticate_admin(
            mock_db, "nonexistent", "password"
        )
        
        assert admin is None
    
    @pytest.mark.asyncio
    async def test_authenticate_admin_inactive(self, mock_admin_user):
        """Тест аутентификации неактивного администратора"""
        mock_admin_user.is_active = False
        mock_db = AsyncMock()
        
        # Мокаем результат запроса к БД
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = mock_admin_user
        mock_db.execute.return_value = mock_result
        
        # Тестируем аутентификацию
        admin = await AdminAuth.authenticate_admin(
            mock_db, "test_admin", "TestPassword123"
        )
        
        assert admin is None
    
    @pytest.mark.asyncio
    async def test_create_session(self, mock_admin_user):
        """Тест создания сессии администратора"""
        mock_db = AsyncMock()
        
        session = await AdminAuth.create_session(
            mock_db, 
            mock_admin_user,
            ip_address="127.0.0.1",
            user_agent="Test Browser"
        )
        
        assert isinstance(session, AdminSession)
        assert session.admin_user_id == mock_admin_user.id
        assert session.ip_address == "127.0.0.1"
        assert session.user_agent == "Test Browser"
        assert session.is_active is True
        assert session.expires_at > datetime.now(timezone.utc)
    
    @pytest.mark.asyncio
    async def test_get_admin_by_id(self, mock_admin_user):
        """Тест получения администратора по ID"""
        mock_db = AsyncMock()
        
        # Мокаем результат запроса к БД
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = mock_admin_user
        mock_db.execute.return_value = mock_result
        
        admin = await AdminAuth.get_admin_by_id(mock_db, 1)
        
        assert admin is not None
        assert admin.id == 1
        assert admin.username == "test_admin"


@pytest.mark.asyncio
class TestAdminAuthEndpoints:
    """Тесты для endpoints аутентификации"""
    
    def test_login_endpoint_success(self):
        """Тест успешного входа через API"""
        client = TestClient(app)
        
        with patch('app.admin.routers.auth.AdminAuth.authenticate_admin') as mock_auth, \
             patch('app.admin.routers.auth.AdminAuth.create_session') as mock_session:
            
            # Мокаем успешную аутентификацию
            mock_admin = AsyncMock()
            mock_admin.id = 1
            mock_admin.username = "test_admin"
            mock_admin.role.value = "admin"
            mock_auth.return_value = mock_admin
            
            # Мокаем создание сессии
            mock_session.return_value = AsyncMock()
            
            # Мокаем создание токенов
            with patch('app.admin.routers.auth.AdminAuth.create_access_token') as mock_access, \
                 patch('app.admin.routers.auth.AdminAuth.create_refresh_token') as mock_refresh:
                
                mock_access.return_value = "test_access_token"
                mock_refresh.return_value = "test_refresh_token"
                
                response = client.post(
                    "/admin/api/auth/login",
                    json={
                        "username": "test_admin",
                        "password": "TestPassword123"
                    }
                )
                
                assert response.status_code == 200
                data = response.json()
                assert "user" in data
                assert "tokens" in data
                assert data["tokens"]["access_token"] == "test_access_token"
                assert data["tokens"]["refresh_token"] == "test_refresh_token"
    
    def test_login_endpoint_invalid_credentials(self):
        """Тест входа с неверными учетными данными"""
        client = TestClient(app)
        
        with patch('app.admin.routers.auth.AdminAuth.authenticate_admin') as mock_auth:
            # Мокаем неудачную аутентификацию
            mock_auth.return_value = None
            
            response = client.post(
                "/admin/api/auth/login",
                json={
                    "username": "test_admin",
                    "password": "wrong_password"
                }
            )
            
            assert response.status_code == 401
            assert "Неверные учетные данные" in response.json()["detail"]
    
    def test_refresh_token_endpoint_success(self):
        """Тест успешного обновления токена"""
        client = TestClient(app)
        
        with patch('app.admin.routers.auth.AdminAuth.verify_token') as mock_verify, \
             patch('app.admin.routers.auth.AdminAuth.get_admin_by_id') as mock_get_admin:
            
            # Мокаем проверку refresh токена
            mock_verify.return_value = {"sub": 1, "username": "test_admin"}
            
            # Мокаем получение администратора
            mock_admin = AsyncMock()
            mock_admin.id = 1
            mock_admin.username = "test_admin"
            mock_admin.role.value = "admin"
            mock_admin.is_active = True
            mock_get_admin.return_value = mock_admin
            
            # Мокаем создание новых токенов
            with patch('app.admin.routers.auth.AdminAuth.create_access_token') as mock_access, \
                 patch('app.admin.routers.auth.AdminAuth.create_refresh_token') as mock_refresh:
                
                mock_access.return_value = "new_access_token"
                mock_refresh.return_value = "new_refresh_token"
                
                response = client.post(
                    "/admin/api/auth/refresh",
                    json={"refresh_token": "valid_refresh_token"}
                )
                
                assert response.status_code == 200
                data = response.json()
                assert data["access_token"] == "new_access_token"
                assert data["refresh_token"] == "new_refresh_token"
    
    def test_refresh_token_endpoint_invalid(self):
        """Тест обновления с невалидным токеном"""
        client = TestClient(app)
        
        with patch('app.admin.routers.auth.AdminAuth.verify_token') as mock_verify:
            # Мокаем неудачную проверку токена
            mock_verify.return_value = None
            
            response = client.post(
                "/admin/api/auth/refresh",
                json={"refresh_token": "invalid_refresh_token"}
            )
            
            assert response.status_code == 401
            assert "Недействительный refresh токен" in response.json()["detail"]


class TestAdminPermissions:
    """Тесты для системы разрешений администраторов"""
    
    def test_admin_user_has_permission(self):
        """Тест проверки разрешений администратора"""
        from app.admin.models import AdminPermission
        
        # Суперпользователь имеет все разрешения
        superuser = AdminUser(
            username="super",
            email="<EMAIL>",
            role=AdminRole.ADMIN,
            is_superuser=True
        )
        
        assert superuser.has_permission(AdminPermission.VIEW_USERS) is True
        assert superuser.has_permission(AdminPermission.DELETE_USERS) is True
        
        # Обычный администратор
        admin = AdminUser(
            username="admin",
            email="<EMAIL>",
            role=AdminRole.ADMIN,
            is_superuser=False
        )
        
        assert admin.has_permission(AdminPermission.VIEW_USERS) is True
        assert admin.has_permission(AdminPermission.CREATE_USERS) is True
        
        # Модератор
        moderator = AdminUser(
            username="moderator",
            email="<EMAIL>",
            role=AdminRole.MODERATOR,
            is_superuser=False
        )
        
        assert moderator.has_permission(AdminPermission.VIEW_USERS) is True
        assert moderator.has_permission(AdminPermission.DELETE_USERS) is False
        
        # Просмотрщик
        viewer = AdminUser(
            username="viewer",
            email="<EMAIL>",
            role=AdminRole.VIEWER,
            is_superuser=False
        )
        
        assert viewer.has_permission(AdminPermission.VIEW_USERS) is True
        assert viewer.has_permission(AdminPermission.CREATE_USERS) is False
    
    def test_admin_user_can_manage_user(self):
        """Тест проверки возможности управления другими администраторами"""
        
        # Суперпользователь может управлять всеми
        superuser = AdminUser(
            id=1,
            username="super",
            email="<EMAIL>",
            role=AdminRole.SUPER_ADMIN,
            is_superuser=True
        )
        
        admin = AdminUser(
            id=2,
            username="admin",
            email="<EMAIL>",
            role=AdminRole.ADMIN,
            is_superuser=False
        )
        
        assert superuser.can_manage_user(admin) is True
        
        # Администратор не может управлять суперпользователем
        assert admin.can_manage_user(superuser) is False
        
        # Администратор не может управлять самим собой
        assert admin.can_manage_user(admin) is False
        
        # Администратор может управлять модератором
        moderator = AdminUser(
            id=3,
            username="moderator",
            email="<EMAIL>",
            role=AdminRole.MODERATOR,
            is_superuser=False
        )
        
        assert admin.can_manage_user(moderator) is True
