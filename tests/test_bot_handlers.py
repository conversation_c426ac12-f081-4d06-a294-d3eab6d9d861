"""
Тесты для обработчиков бота
"""

import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from aiogram.types import Message, CallbackQuery, User as TelegramUser
from aiogram.fsm.context import FSMContext

from app.bots.main_bot.handlers.start import cmd_start, cmd_help, back_to_main_menu
from app.database.models import User


class TestStartHandlers:
    """Тесты для обработчиков команд start и help"""

    @pytest.mark.asyncio
    async def test_start_command_new_user(self):
        """Тест команды /start для нового пользователя"""

        # Мокаем функцию создания пользователя
        with patch('app.bots.main_bot.handlers.start.get_or_create_user') as mock_create_user:
            mock_user = User(
                telegram_id=123456789,
                username="testuser",
                first_name="Test",
                last_name="User",
                language_code="ru",
                referral_code="TEST1234"
            )
            mock_create_user.return_value = mock_user

            # Создаем мок сообщения
            message = MagicMock(spec=Message)
            message.from_user = TelegramUser(
                id=123456789,
                is_bot=False,
                first_name="Test",
                last_name="User",
                username="testuser",
                language_code="ru"
            )
            message.answer = AsyncMock()

            # Создаем мок состояния
            state = MagicMock(spec=FSMContext)
            state.clear = AsyncMock()

            # Выполняем обработчик
            await cmd_start(message, state)

            # Проверяем что функция создания пользователя была вызвана
            mock_create_user.assert_called_once()
            call_args = mock_create_user.call_args[0][0]
            assert call_args['telegram_id'] == 123456789
            assert call_args['username'] == "testuser"
            assert call_args['first_name'] == "Test"

            # Проверяем что сообщение было отправлено
            message.answer.assert_called_once()
            call_args = message.answer.call_args[0][0]
            assert "Добро пожаловать" in call_args
            assert "Test" in call_args

    @pytest.mark.asyncio
    async def test_start_command_error_handling(self):
        """Тест обработки ошибок в команде /start"""

        # Мокаем функцию создания пользователя с ошибкой
        with patch('app.bots.main_bot.handlers.start.get_or_create_user') as mock_create_user:
            mock_create_user.side_effect = Exception("Database error")

            # Создаем мок сообщения
            message = MagicMock(spec=Message)
            message.from_user = TelegramUser(
                id=123456789,
                is_bot=False,
                first_name="Test"
            )
            message.answer = AsyncMock()

            # Создаем мок состояния
            state = MagicMock(spec=FSMContext)

            # Выполняем обработчик
            await cmd_start(message, state)

            # Проверяем что сообщение об ошибке было отправлено
            message.answer.assert_called_once()
            call_args = message.answer.call_args[0][0]
            assert "Произошла ошибка" in call_args

    @pytest.mark.asyncio
    async def test_help_command(self):
        """Тест команды /help"""

        # Создаем мок сообщения
        message = MagicMock(spec=Message)
        message.from_user = TelegramUser(
            id=123456789,
            is_bot=False,
            first_name="Test"
        )
        message.answer = AsyncMock()

        # Выполняем обработчик
        await cmd_help(message)

        # Проверяем что сообщение было отправлено
        message.answer.assert_called_once()
        call_args = message.answer.call_args[0][0]
        assert "Помощь по использованию бота" in call_args
        assert "/start" in call_args
        assert "/help" in call_args
        assert "/support" in call_args

    @pytest.mark.asyncio
    async def test_back_to_main_menu(self):
        """Тест возврата в главное меню"""

        # Создаем мок callback query
        callback = MagicMock(spec=CallbackQuery)
        callback.from_user = TelegramUser(
            id=123456789,
            is_bot=False,
            first_name="Test"
        )
        callback.message = MagicMock()
        callback.message.edit_text = AsyncMock()
        callback.answer = AsyncMock()

        # Создаем мок состояния
        state = MagicMock(spec=FSMContext)
        state.clear = AsyncMock()

        # Выполняем обработчик
        await back_to_main_menu(callback, state)

        # Проверяем что сообщение было отредактировано
        callback.message.edit_text.assert_called_once()
        call_args = callback.message.edit_text.call_args[0][0]
        assert "Главное меню" in call_args

        # Проверяем что callback был отвечен
        callback.answer.assert_called_once()

    @pytest.mark.asyncio
    async def test_back_to_main_menu_error_handling(self):
        """Тест обработки ошибок при возврате в главное меню"""

        # Создаем мок callback query с ошибкой
        callback = MagicMock(spec=CallbackQuery)
        callback.from_user = TelegramUser(
            id=123456789,
            is_bot=False,
            first_name="Test"
        )
        callback.message = MagicMock()
        callback.message.edit_text = AsyncMock(side_effect=Exception("Edit error"))
        callback.answer = AsyncMock()

        # Создаем мок состояния
        state = MagicMock(spec=FSMContext)

        # Выполняем обработчик - он должен обработать ошибку
        await back_to_main_menu(callback, state)

        # Проверяем что callback был отвечен с ошибкой
        callback.answer.assert_called_once()
        call_args = callback.answer.call_args
        assert call_args[1]['show_alert'] is True
