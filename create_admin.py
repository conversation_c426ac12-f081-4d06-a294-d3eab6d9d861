#!/usr/bin/env python3
"""
Простой скрипт для создания первого администратора
"""

import asyncio
import sys
from datetime import datetime, timezone

from app.database.database import AsyncSessionLocal, Base, engine
from app.admin.models import AdminUser, AdminRole
from app.admin.auth import AdminAuth


async def create_admin():
    """Создает первого администратора"""
    
    # Создаем таблицы если их нет (не пересоздаем)
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    async with AsyncSessionLocal() as db:
        # Создаем администратора
        admin = AdminUser(
            username="admin",
            email="<EMAIL>",
            hashed_password=AdminAuth.get_password_hash("admin123"),
            full_name="Admin User",
            role=AdminRole.SUPER_ADMIN.value,
            is_active=True,
            is_superuser=True,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
            notes="Создан через скрипт"
        )
        
        db.add(admin)
        await db.commit()
        await db.refresh(admin)
        
        print(f"✅ Администратор создан!")
        print(f"   Username: {admin.username}")
        print(f"   Email: {admin.email}")
        print(f"   Password: admin123")
        print(f"   ID: {admin.id}")


if __name__ == "__main__":
    asyncio.run(create_admin())
