# Database
DATABASE_URL=postgresql+asyncpg://unveil_user:unveil_password@localhost:5432/unveil_vpn
REDIS_URL=redis://localhost:6379/0

# Telegram Bots
MAIN_BOT_TOKEN=**********************************************
MONITOR_BOT_TOKEN=8028743007:AAFEujhViZhhLhNGQ2Th6gNkGeo9J2BW_TY
SUPPORT_BOT_TOKEN=7971569021:AAHo_Mbe4d3FLkWQbqmeS5rRmaTxQ6utxGg
ADMIN_TELEGRAM_IDS=5777893929

# Marzban API
MARZBAN_API_URL=https://panel.snaplyze.me/api/v1
MARZBAN_USERNAME=admin
MARZBAN_PASSWORD=admin

# Payment Systems
YUKASSA_SHOP_ID=your_shop_id
YUKASSA_SECRET_KEY=your_secret_key
CRYPTOMUS_MERCHANT_ID=your_merchant_id
CRYPTOMUS_API_KEY=your_api_key

# Security
SECRET_KEY=your_secret_key_for_jwt_here_change_in_production
ENCRYPTION_KEY=your_encryption_key_for_sensitive_data_32_chars

# Monitoring
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000

# Web
WEB_HOST=0.0.0.0
WEB_PORT=8000
DOMAIN=your-domain.com

# Environment
ENVIRONMENT=development
DEBUG=True
LOG_LEVEL=INFO

# Database connection pool
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30

# Redis settings
REDIS_POOL_SIZE=10
REDIS_TIMEOUT=5
